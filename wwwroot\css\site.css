/* ===== DESIGN SYSTEM ===== */
:root {
  /* Primary Colors - Modern Purple/Blue Gradient */
  --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  --primary-dark: #4c63d2;
  --primary-light: #8fa4f3;
  --primary-50: #f0f4ff;
  --primary-100: #e0e7ff;
  --primary-500: #667eea;
  --primary-600: #5a67d8;
  --primary-700: #4c51bf;

  /* Secondary Colors - Vibrant Gradients */
  --secondary-gradient: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  --accent-gradient: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  --success-gradient: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
  --warning-gradient: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
  --info-gradient: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);

  /* Neutral Colors - Modern Gray Scale */
  --white: #ffffff;
  --gray-50: #f9fafb;
  --gray-100: #f3f4f6;
  --gray-200: #e5e7eb;
  --gray-300: #d1d5db;
  --gray-400: #9ca3af;
  --gray-500: #6b7280;
  --gray-600: #4b5563;
  --gray-700: #374151;
  --gray-800: #1f2937;
  --gray-900: #111827;

  /* Shadows - Layered Design */
  --shadow-xs: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-sm: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  --shadow-colored: 0 10px 25px -5px rgba(102, 126, 234, 0.25);

  /* Border Radius - Consistent Spacing */
  --radius-none: 0;
  --radius-sm: 0.375rem;
  --radius-md: 0.5rem;
  --radius-lg: 0.75rem;
  --radius-xl: 1rem;
  --radius-2xl: 1.5rem;
  --radius-3xl: 2rem;
  --radius-full: 9999px;

  /* Spacing Scale */
  --space-1: 0.25rem;
  --space-2: 0.5rem;
  --space-3: 0.75rem;
  --space-4: 1rem;
  --space-5: 1.25rem;
  --space-6: 1.5rem;
  --space-8: 2rem;
  --space-10: 2.5rem;
  --space-12: 3rem;
  --space-16: 4rem;
  --space-20: 5rem;

  /* Transitions */
  --transition-fast: all 0.15s cubic-bezier(0.4, 0, 0.2, 1);
  --transition-normal: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  --transition-slow: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);

  /* Typography */
  --font-sans: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  --font-mono: 'JetBrains Mono', 'Fira Code', Consolas, monospace;
}

/* ===== BASE STYLES ===== */
html {
  font-size: 14px;
  scroll-behavior: smooth;
}

@media (min-width: 768px) {
  html {
    font-size: 16px;
  }
}

body {
  font-family: var(--font-sans);
  line-height: 1.6;
  color: var(--gray-800);
  background: var(--gray-50);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* ===== FOCUS STYLES ===== */
.btn:focus, .btn:active:focus, .btn-link.nav-link:focus, .form-control:focus, .form-check-input:focus {
  box-shadow: 0 0 0 0.1rem white, 0 0 0 0.25rem var(--primary-light);
  outline: none;
}

/* ===== HERO SECTION ===== */
.hero-section {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); /* Fallback gradient */
  background-image: url('/image/bia.png?v=3');
  background-position: center center;
  background-size: cover;
  background-repeat: no-repeat;
  background-attachment: fixed; /* Parallax effect */
  min-height: 500px;
  position: relative;
  overflow: hidden;
  display: flex;
  align-items: center;
}

.hero-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    135deg,
    rgba(0, 0, 0, 0.6) 0%,
    rgba(0, 0, 0, 0.3) 50%,
    rgba(0, 0, 0, 0.5) 100%
  ); /* Gradient overlay để text dễ đọc và tạo depth */
  pointer-events: none;
}

.hero-section::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="dots" width="20" height="20" patternUnits="userSpaceOnUse"><circle cx="10" cy="10" r="1" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23dots)"/></svg>');
  pointer-events: none;
}

.hero-content {
  position: relative;
  z-index: 2;
}

.hero-title {
  font-size: 3.5rem;
  font-weight: 800;
  color: white;
  margin-bottom: var(--space-6);
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  line-height: 1.2;
}

.hero-subtitle {
  font-size: 1.25rem;
  color: rgba(255, 255, 255, 0.9);
  margin-bottom: var(--space-8);
  font-weight: 400;
  line-height: 1.6;
}

.hero-icon {
  position: absolute;
  right: 10%;
  top: 50%;
  transform: translateY(-50%);
  font-size: 8rem;
  color: rgba(255, 255, 255, 0.1);
  z-index: 1;
}

@media (max-width: 768px) {
  .hero-section {
    min-height: 400px;
    background-attachment: scroll; /* Tắt parallax trên mobile để tăng performance */
  }

  .hero-title {
    font-size: 2.5rem;
  }

  .hero-subtitle {
    font-size: 1.1rem;
  }

  .hero-icon {
    display: none;
  }
}

/* ===== CARD STYLES ===== */
.card {
  border: none;
  border-radius: var(--radius-xl);
  background: var(--white);
  box-shadow: var(--shadow-sm);
  transition: var(--transition-normal);
  overflow: hidden;
}

.card:hover {
  transform: translateY(-8px);
  box-shadow: var(--shadow-xl);
}

.card-header {
  border-radius: var(--radius-xl) var(--radius-xl) 0 0 !important;
  border: none;
  background: var(--gray-50);
  padding: var(--space-6);
}

.card-body {
  padding: var(--space-6);
}

.card-footer {
  background: var(--gray-50);
  border: none;
  padding: var(--space-6);
}

/* Glass Card Effect */
.card-glass {
  background: rgba(255, 255, 255, 0.25);
  -webkit-backdrop-filter: blur(10px);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.18);
  box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
}

/* ===== BUTTON STYLES ===== */
.btn {
  border-radius: var(--radius-full);
  padding: var(--space-3) var(--space-6);
  font-weight: 600;
  font-size: 0.875rem;
  transition: var(--transition-normal);
  border: none;
  position: relative;
  overflow: hidden;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: var(--transition-normal);
}

.btn:hover::before {
  left: 100%;
}

.btn-lg {
  padding: var(--space-4) var(--space-8);
  font-size: 1rem;
}

.btn-sm {
  padding: var(--space-2) var(--space-4);
  font-size: 0.75rem;
}

/* Primary Button */
.btn-primary {
  background: var(--primary-gradient);
  color: white;
  box-shadow: var(--shadow-colored);
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-xl);
  color: white;
}

.btn-primary:active {
  transform: translateY(0);
}

/* Secondary Button */
.btn-secondary {
  background: var(--gray-600);
  color: white;
}

.btn-secondary:hover {
  background: var(--gray-700);
  transform: translateY(-2px);
  color: white;
}

/* Success Button */
.btn-success {
  background: var(--success-gradient);
  color: white;
}

.btn-success:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
  color: white;
}

/* Outline Buttons */
.btn-outline-primary {
  border: 2px solid var(--primary-500);
  color: var(--primary-500);
  background: transparent;
}

.btn-outline-primary:hover {
  background: var(--primary-gradient);
  color: white;
  transform: translateY(-2px);
}

/* Ghost Button */
.btn-ghost {
  background: rgba(255, 255, 255, 0.1);
  color: white;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.btn-ghost:hover {
  background: rgba(255, 255, 255, 0.2);
  color: white;
  transform: translateY(-2px);
}

/* ===== NAVIGATION STYLES ===== */
.navbar {
  -webkit-backdrop-filter: blur(10px);
  backdrop-filter: blur(10px);
  background: rgba(255, 255, 255, 0.95) !important;
  box-shadow: var(--shadow-sm);
  border-bottom: 1px solid var(--gray-200);
}

.navbar-brand {
  font-weight: 800;
  font-size: 1.5rem;
  color: var(--gray-800) !important;
  display: flex;
  align-items: center;
  gap: var(--space-2);
}

.navbar-brand i {
  font-size: 1.75rem;
  background: var(--primary-gradient);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.navbar-nav .nav-link {
  font-weight: 500;
  margin: 0 var(--space-2);
  padding: var(--space-2) var(--space-4) !important;
  border-radius: var(--radius-full);
  transition: var(--transition-normal);
  color: var(--gray-700) !important;
  position: relative;
}

.navbar-nav .nav-link:hover {
  background: var(--gray-100);
  color: var(--primary-600) !important;
  transform: translateY(-1px);
}

.navbar-nav .nav-link.active {
  background: var(--primary-gradient);
  color: white !important;
}

/* Mobile Navigation */
.navbar-toggler {
  border: none;
  padding: var(--space-2);
}

.navbar-toggler:focus {
  box-shadow: none;
}

/* ===== FORM STYLES ===== */
.form-control {
  border-radius: var(--radius-lg);
  border: 2px solid var(--gray-300);
  padding: var(--space-3) var(--space-4);
  transition: var(--transition-normal);
  background: var(--white);
  font-size: 0.875rem;
}

.form-control:focus {
  border-color: var(--primary-500);
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
  outline: none;
}

.form-control::placeholder {
  color: var(--gray-500);
}

.form-label {
  font-weight: 600;
  color: var(--gray-700);
  margin-bottom: var(--space-2);
}

.form-check-input {
  border-radius: var(--radius-sm);
  border: 2px solid var(--gray-300);
  transition: var(--transition-normal);
}

.form-check-input:checked {
  background-color: var(--primary-500);
  border-color: var(--primary-500);
}

.form-check-input:focus {
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.form-check-label {
  font-weight: 500;
  color: var(--gray-700);
}

/* Input Groups */
.input-group .form-control {
  border-radius: var(--radius-lg) 0 0 var(--radius-lg);
}

.input-group .btn {
  border-radius: 0 var(--radius-lg) var(--radius-lg) 0;
}

/* Select Styles */
.form-select {
  border-radius: var(--radius-lg);
  border: 2px solid var(--gray-300);
  padding: var(--space-3) var(--space-4);
  transition: var(--transition-normal);
}

.form-select:focus {
  border-color: var(--primary-500);
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

/* Question Container Styles */
.question-container {
  background: #f8f9fa;
  border-radius: 15px;
  transition: all 0.3s ease;
}

.question-container:hover {
  background: #e9ecef;
}

.question-title {
  color: #495057;
  margin-bottom: 1rem;
}

/* Progress Bar Styles */
.progress {
  border-radius: 10px;
  height: 10px;
}

.progress-bar {
  border-radius: 10px;
  transition: width 0.6s ease;
}

/* Badge Styles */
.badge {
  border-radius: 15px;
  padding: 0.5em 0.75em;
}

/* Footer Styles */
footer {
  background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
}

/* Animation Classes */
.fade-in {
  animation: fadeIn 0.6s ease-in;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(20px); }
  to { opacity: 1; transform: translateY(0); }
}

.slide-up {
  animation: slideUp 0.6s ease-out;
}

@keyframes slideUp {
  from { transform: translateY(30px); opacity: 0; }
  to { transform: translateY(0); opacity: 1; }
}

/* Responsive Styles */
@media (max-width: 768px) {
  .hero-section {
    min-height: 300px;
  }

  .btn-lg {
    padding: 0.5rem 1.5rem;
    font-size: 1rem;
  }

  .display-4 {
    font-size: 2rem;
  }
}

/* ===== FEATURE CARDS ===== */
.feature-card {
  background: var(--white);
  border-radius: var(--radius-2xl);
  box-shadow: var(--shadow-sm);
  transition: var(--transition-normal);
  border: 1px solid var(--gray-200);
}

.feature-card:hover {
  transform: translateY(-8px);
  box-shadow: var(--shadow-xl);
}

.feature-icon-wrapper {
  display: flex;
  justify-content: center;
  align-items: center;
}

.feature-icon-bg {
  width: 80px;
  height: 80px;
  border-radius: var(--radius-full);
  background: var(--primary-gradient);
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;
}

.feature-icon-bg::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  transition: var(--transition-normal);
}

.feature-card:hover .feature-icon-bg::before {
  left: 100%;
}

.feature-icon {
  font-size: 2rem;
  color: white;
  z-index: 2;
}

/* ===== UTILITY CLASSES ===== */
.text-gradient {
  background: var(--primary-gradient);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-weight: 800;
}

.shadow-hover {
  transition: var(--transition-normal);
}

.shadow-hover:hover {
  box-shadow: var(--shadow-xl);
}

.min-vh-50 {
  min-height: 50vh;
}

.bg-gradient-primary {
  background: var(--primary-gradient);
}

.bg-gradient-secondary {
  background: var(--secondary-gradient);
}

.bg-gradient-success {
  background: var(--success-gradient);
}

.bg-gradient-warning {
  background: var(--warning-gradient);
}

.bg-gradient-info {
  background: var(--info-gradient);
}

/* ===== EMPTY STATE ===== */
.empty-state {
  padding: var(--space-16) var(--space-8);
}

.empty-state i {
  opacity: 0.5;
}

/* ===== PRICE TAG ===== */
.price-tag {
  text-align: left;
}

.price-tag .h5 {
  line-height: 1;
}

.price-tag small {
  font-size: 0.75rem;
  margin-top: -2px;
}

/* ===== FOOTER STYLES ===== */
footer {
  position: relative;
  overflow: hidden;
}

footer::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="footerPattern" width="40" height="40" patternUnits="userSpaceOnUse"><circle cx="20" cy="20" r="1" fill="white" opacity="0.05"/></pattern></defs><rect width="100" height="100" fill="url(%23footerPattern)"/></svg>');
  pointer-events: none;
}

.hover-opacity-100:hover {
  opacity: 1 !important;
  transition: var(--transition-fast);
}

/* ===== NAVBAR ENHANCEMENTS ===== */
.navbar {
  transition: var(--transition-normal);
}

.navbar.scrolled {
  background: rgba(255, 255, 255, 0.98) !important;
  box-shadow: var(--shadow-lg);
}

/* ===== RESPONSIVE ENHANCEMENTS ===== */
@media (max-width: 768px) {
  .hero-title {
    font-size: 2.5rem;
  }

  .hero-subtitle {
    font-size: 1.1rem;
  }

  .feature-card {
    margin-bottom: var(--space-6);
  }

  .navbar-nav .nav-link {
    padding: var(--space-3) var(--space-4) !important;
    margin: var(--space-1) 0;
  }
}

@media (max-width: 576px) {
  .hero-title {
    font-size: 2rem;
  }

  .display-5 {
    font-size: 2rem;
  }

  .btn-lg {
    padding: var(--space-3) var(--space-6);
    font-size: 0.9rem;
  }
}

html, body {
  height: 100%;
  margin: 0;
  padding: 0;
  overflow-x: hidden;
}

body {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  margin-bottom: 0 !important;
}

main {
  flex: 1 0 auto;
}

footer {
  flex-shrink: 0;
  margin-bottom: 0 !important;
  padding-bottom: 0 !important;
  position: relative;
  width: 100%;
}

.form-floating > .form-control-plaintext::placeholder, .form-floating > .form-control::placeholder {
  color: var(--bs-secondary-color);
  text-align: end;
}

.form-floating > .form-control-plaintext:focus::placeholder, .form-floating > .form-control:focus::placeholder {
  text-align: start;
}
