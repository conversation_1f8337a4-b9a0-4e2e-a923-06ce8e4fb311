@model Exam
@{
    ViewData["Title"] = Model.Title;
}

<div class="row">
    <div class="col-12">
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a asp-controller="Home" asp-action="Index">Trang chủ</a></li>
                <li class="breadcrumb-item"><a asp-action="Index">Bài kiểm tra</a></li>
                <li class="breadcrumb-item active">@Model.Title</li>
            </ol>
        </nav>
    </div>
</div>

<div class="row">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header bg-primary text-white">
                <h1 class="card-title mb-0">@Model.Title</h1>
            </div>
            <div class="card-body">
                <p class="card-text lead">@Model.Description</p>
                
                <div class="row mt-4">
                    <div class="col-md-6">
                        <h5>Thông tin bài thi</h5>
                        <ul class="list-unstyled">
                            <li><i class="bi bi-clock text-primary"></i> <strong>Thời gian:</strong> @Model.Duration phút</li>
                            <li><i class="bi bi-question-circle text-success"></i> <strong>Số câu hỏi:</strong> @Model.TotalQuestions câu</li>
                            <li><i class="bi bi-calendar text-info"></i> <strong>Ngày tạo:</strong> @Model.CreatedDate.ToString("dd/MM/yyyy")</li>
                            <li><i class="bi bi-check-circle text-warning"></i> <strong>Trạng thái:</strong> @(Model.IsActive ? "Đang hoạt động" : "Tạm dừng")</li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h5>Hướng dẫn làm bài</h5>
                        <ul class="list-unstyled">
                            <li><i class="bi bi-1-circle"></i> Đọc kỹ câu hỏi trước khi trả lời</li>
                            <li><i class="bi bi-2-circle"></i> Chọn đáp án đúng nhất</li>
                            <li><i class="bi bi-3-circle"></i> Kiểm tra lại trước khi nộp bài</li>
                            <li><i class="bi bi-4-circle"></i> Thời gian làm bài có giới hạn</li>
                        </ul>
                    </div>
                </div>
                
                @if (Model.Questions != null && Model.Questions.Any())
                {
                    <div class="mt-4">
                        <h5>Xem trước câu hỏi</h5>
                        <div class="alert alert-info">
                            <i class="bi bi-info-circle"></i>
                            Bài thi này có @Model.Questions.Count câu hỏi với các chủ đề đa dạng.
                            Hãy chuẩn bị kiến thức tốt trước khi bắt đầu.
                        </div>
                    </div>
                }
            </div>
        </div>
    </div>
    
    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">Hành động</h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    @if (Model.IsActive)
                    {
                        <a asp-action="Take" asp-route-id="@Model.Id" class="btn btn-primary btn-lg">
                            <i class="bi bi-play-circle"></i> Bắt đầu làm bài
                        </a>
                    }
                    else
                    {
                        <button class="btn btn-secondary btn-lg" disabled>
                            <i class="bi bi-pause-circle"></i> Bài thi tạm dừng
                        </button>
                    }
                    <a asp-action="Index" class="btn btn-outline-secondary">
                        <i class="bi bi-arrow-left"></i> Quay lại danh sách
                    </a>
                    @if (User.Identity.IsAuthenticated)
                    {
                        <a asp-action="MyResults" class="btn btn-success">
                            <i class="bi bi-clipboard-data"></i> Xem kết quả của tôi
                        </a>
                    }
                </div>
            </div>
        </div>
        
        <div class="card mt-3">
            <div class="card-header">
                <h5 class="mb-0">Thống kê</h5>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-6">
                        <div class="border-end">
                            <h4 class="text-primary">@Model.TotalQuestions</h4>
                            <small class="text-muted">Câu hỏi</small>
                        </div>
                    </div>
                    <div class="col-6">
                        <h4 class="text-success">@Model.Duration</h4>
                        <small class="text-muted">Phút</small>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="card mt-3">
            <div class="card-header">
                <h5 class="mb-0">Gợi ý</h5>
            </div>
            <div class="card-body">
                <ul class="list-unstyled">
                    <li class="mb-2">
                        <a asp-controller="Survey" asp-action="Index" class="text-decoration-none">
                            <i class="bi bi-clipboard-check text-primary"></i> Làm khảo sát tính cách
                        </a>
                    </li>
                    <li class="mb-2">
                        <a asp-controller="School" asp-action="Index" class="text-decoration-none">
                            <i class="bi bi-building text-success"></i> Khám phá trường học
                        </a>
                    </li>
                </ul>
            </div>
        </div>
    </div>
</div>

@if (!User.Identity.IsAuthenticated)
{
    <div class="row mt-4">
        <div class="col-12">
            <div class="alert alert-warning text-center">
                <i class="bi bi-exclamation-triangle"></i> 
                Bạn cần <a asp-controller="Account" asp-action="Login" class="alert-link">đăng nhập</a> 
                để lưu kết quả bài thi.
            </div>
        </div>
    </div>
}
