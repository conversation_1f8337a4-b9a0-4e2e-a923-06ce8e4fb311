@model List<Survey>
@{
    ViewData["Title"] = "Khảo sát tính cách";
}

<div class="row mb-4">
    <div class="col-12">
        <h1 class="fw-bold">Khảo sát tính cách</h1>
        <p class="text-muted">Khám phá tính cách của bạn thông qua các bài khảo sát chuyên nghiệp</p>
    </div>
</div>

<div class="row">
    @if (Model != null && Model.Any())
    {
        @foreach (var survey in Model)
        {
            <div class="col-lg-6 col-md-12 mb-4">
                <div class="card h-100 shadow-sm border-0">
                    <div class="card-header bg-gradient-primary text-white">
                        <div class="d-flex justify-content-between align-items-center">
                            <h5 class="card-title mb-0">@survey.Title</h5>
                            <span class="badge bg-light text-dark">@survey.Type.ToString()</span>
                        </div>
                    </div>
                    <div class="card-body d-flex flex-column">
                        <p class="card-text">@survey.Description</p>
                        
                        <div class="survey-info mb-3">
                            <div class="row text-center">
                                <div class="col-6">
                                    <div class="border-end">
                                        <i class="bi bi-calendar3 text-primary fs-4"></i>
                                        <h6 class="mt-2">@survey.CreatedDate.ToString("dd/MM/yyyy")</h6>
                                        <small class="text-muted">Ngày tạo</small>
                                    </div>
                                </div>
                                <div class="col-6">
                                    <i class="bi bi-people text-success fs-4"></i>
                                    <h6 class="mt-2">Miễn phí</h6>
                                    <small class="text-muted">Hoàn toàn miễn phí</small>
                                </div>
                            </div>
                        </div>
                        
                        @if (survey.Type == SurveyType.MBTI)
                        {
                            <div class="alert alert-info">
                                <small>
                                    <i class="bi bi-info-circle"></i>
                                    Bài test MBTI giúp bạn hiểu rõ về 16 loại tính cách khác nhau
                                </small>
                            </div>
                        }
                        
                        <div class="mt-auto">
                            <div class="d-grid gap-2">
                                <a asp-action="Take" asp-route-id="@survey.Id" class="btn btn-primary btn-lg">
                                    <i class="bi bi-play-circle"></i> Bắt đầu khảo sát
                                </a>
                                <a asp-action="Details" asp-route-id="@survey.Id" class="btn btn-outline-secondary">
                                    <i class="bi bi-info-circle"></i> Xem chi tiết
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        }
    }
    else
    {
        <div class="col-12 text-center">
            <div class="alert alert-info">
                <i class="bi bi-info-circle"></i> Hiện tại chưa có khảo sát nào.
            </div>
        </div>
    }
</div>

@if (User.Identity.IsAuthenticated)
{
    <div class="row mt-4">
        <div class="col-12 text-center">
            <a asp-action="MyResults" class="btn btn-success btn-lg">
                <i class="bi bi-clipboard-data"></i> Xem kết quả khảo sát của tôi
            </a>
        </div>
    </div>
}
else
{
    <div class="row mt-4">
        <div class="col-12">
            <div class="alert alert-warning text-center">
                <i class="bi bi-exclamation-triangle"></i> 
                Bạn cần <a asp-controller="Account" asp-action="Login" class="alert-link">đăng nhập</a> 
                để lưu kết quả khảo sát.
            </div>
        </div>
    </div>
}

<!-- MBTI Information Section -->
<div class="row mt-5">
    <div class="col-12">
        <h3>Về bài test MBTI</h3>
        <div class="row">
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="text-center">
                    <i class="bi bi-person-check display-4 text-primary"></i>
                    <h5 class="mt-2">16 Loại tính cách</h5>
                    <p class="text-muted">Khám phá 1 trong 16 loại tính cách MBTI</p>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="text-center">
                    <i class="bi bi-lightbulb display-4 text-success"></i>
                    <h5 class="mt-2">Hiểu bản thân</h5>
                    <p class="text-muted">Tìm hiểu điểm mạnh và điểm yếu của bạn</p>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="text-center">
                    <i class="bi bi-briefcase display-4 text-info"></i>
                    <h5 class="mt-2">Gợi ý nghề nghiệp</h5>
                    <p class="text-muted">Nhận gợi ý nghề nghiệp phù hợp</p>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="text-center">
                    <i class="bi bi-graph-up display-4 text-warning"></i>
                    <h5 class="mt-2">Phát triển bản thân</h5>
                    <p class="text-muted">Lộ trình phát triển cá nhân</p>
                </div>
            </div>
        </div>
    </div>
</div>
