@model UserManagementViewModel
@{
    ViewData["Title"] = "Chi tiết người dùng";
}

<div class="row mb-4">
    <div class="col-12">
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item">
                    <a asp-action="Index" asp-controller="Dashboard">Dashboard</a>
                </li>
                <li class="breadcrumb-item">
                    <a asp-action="Index">Quản lý người dùng</a>
                </li>
                <li class="breadcrumb-item active">Chi tiết</li>
            </ol>
        </nav>
        
        <h1 class="fw-bold">
            <i class="bi bi-person-circle"></i> Chi tiết người dùng
        </h1>
        <p class="text-muted">Thông tin chi tiết về tài khoản người dùng</p>
    </div>
</div>

<div class="row">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-info-circle"></i> Thông tin tài khoản
                </h5>
            </div>
            <div class="card-body">
                <div class="row mb-3">
                    <div class="col-sm-3">
                        <strong>ID:</strong>
                    </div>
                    <div class="col-sm-9">
                        <code>@Model.Id</code>
                    </div>
                </div>
                
                <div class="row mb-3">
                    <div class="col-sm-3">
                        <strong>Email:</strong>
                    </div>
                    <div class="col-sm-9">
                        @Model.Email
                        @if (Model.EmailConfirmed)
                        {
                            <i class="bi bi-check-circle text-success ms-2" title="Email đã xác thực"></i>
                        }
                        else
                        {
                            <i class="bi bi-x-circle text-danger ms-2" title="Email chưa xác thực"></i>
                        }
                    </div>
                </div>
                
                <div class="row mb-3">
                    <div class="col-sm-3">
                        <strong>Tên đăng nhập:</strong>
                    </div>
                    <div class="col-sm-9">
                        @Model.UserName
                    </div>
                </div>
                
                <div class="row mb-3">
                    <div class="col-sm-3">
                        <strong>Vai trò:</strong>
                    </div>
                    <div class="col-sm-9">
                        @if (Model.Roles.Any())
                        {
                            @foreach (var role in Model.Roles)
                            {
                                <span class="badge bg-@(role == "Admin" ? "danger" : role == "Moderator" ? "warning" : "primary") me-1">
                                    <i class="bi bi-shield"></i> @role
                                </span>
                            }
                        }
                        else
                        {
                            <span class="text-muted">Chưa có vai trò</span>
                        }
                    </div>
                </div>
                
                <div class="row mb-3">
                    <div class="col-sm-3">
                        <strong>Trạng thái:</strong>
                    </div>
                    <div class="col-sm-9">
                        @if (Model.Status == "Hoạt động")
                        {
                            <span class="badge bg-success">
                                <i class="bi bi-check-circle"></i> Hoạt động
                            </span>
                        }
                        else
                        {
                            <span class="badge bg-danger">
                                <i class="bi bi-lock"></i> Bị khóa
                            </span>
                            @if (Model.LockoutEnd.HasValue)
                            {
                                <div class="small text-muted mt-1">
                                    Khóa đến: @Model.LockoutEnd.Value.ToString("dd/MM/yyyy HH:mm")
                                </div>
                            }
                        }
                    </div>
                </div>
                
                <div class="row mb-3">
                    <div class="col-sm-3">
                        <strong>Email xác thực:</strong>
                    </div>
                    <div class="col-sm-9">
                        @if (Model.EmailConfirmed)
                        {
                            <span class="text-success">
                                <i class="bi bi-check-circle"></i> Đã xác thực
                            </span>
                        }
                        else
                        {
                            <span class="text-danger">
                                <i class="bi bi-x-circle"></i> Chưa xác thực
                            </span>
                        }
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="bi bi-tools"></i> Thao tác quản lý
                </h6>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a asp-action="ManageRoles" asp-route-id="@Model.Id" class="btn btn-warning">
                        <i class="bi bi-person-gear"></i> Quản lý vai trò
                    </a>
                    
                    @if (Model.Status == "Hoạt động")
                    {
                        <form asp-action="ToggleLockout" asp-route-id="@Model.Id" method="post">
                            <button type="submit" class="btn btn-danger w-100" 
                                    onclick="return confirm('Bạn có chắc muốn khóa tài khoản này?')">
                                <i class="bi bi-lock"></i> Khóa tài khoản
                            </button>
                        </form>
                    }
                    else
                    {
                        <form asp-action="ToggleLockout" asp-route-id="@Model.Id" method="post">
                            <button type="submit" class="btn btn-success w-100" 
                                    onclick="return confirm('Bạn có chắc muốn mở khóa tài khoản này?')">
                                <i class="bi bi-unlock"></i> Mở khóa tài khoản
                            </button>
                        </form>
                    }
                    
                    @if (!Model.Roles.Contains("Admin"))
                    {
                        <form asp-action="Delete" asp-route-id="@Model.Id" method="post">
                            <button type="submit" class="btn btn-outline-danger w-100" 
                                    onclick="return confirm('Bạn có chắc muốn xóa tài khoản này? Hành động này không thể hoàn tác!')">
                                <i class="bi bi-trash"></i> Xóa tài khoản
                            </button>
                        </form>
                    }
                </div>
            </div>
        </div>
        
        <div class="card mt-3">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="bi bi-activity"></i> Hoạt động
                </h6>
            </div>
            <div class="card-body">
                <div class="text-center">
                    <div class="row">
                        <div class="col-6">
                            <h5 class="text-primary">0</h5>
                            <small class="text-muted">Bài thi</small>
                        </div>
                        <div class="col-6">
                            <h5 class="text-success">0</h5>
                            <small class="text-muted">Khảo sát</small>
                        </div>
                    </div>
                </div>
                <hr>
                <p class="text-muted text-center mb-0">
                    <small>Thống kê hoạt động sẽ được cập nhật sau</small>
                </p>
            </div>
        </div>
    </div>
</div>

<div class="row mt-4">
    <div class="col-12">
        <div class="d-flex justify-content-between">
            <a asp-action="Index" class="btn btn-secondary">
                <i class="bi bi-arrow-left"></i> Quay lại danh sách
            </a>
            
            <div>
                <a asp-action="ManageRoles" asp-route-id="@Model.Id" class="btn btn-warning">
                    <i class="bi bi-person-gear"></i> Quản lý vai trò
                </a>
            </div>
        </div>
    </div>
</div>
