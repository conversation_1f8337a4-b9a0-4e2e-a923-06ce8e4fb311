{"Version": 1, "Hash": "VQEKxZt+ekmTolk7ICB9CctyRX0ITWs3MWdashoHTQs=", "Source": "MyAspNetCoreApp", "BasePath": "_content/MyAspNetCoreApp", "Mode": "<PERSON><PERSON><PERSON>", "ManifestType": "Build", "ReferencedProjectsConfiguration": [], "DiscoveryPatterns": [{"Name": "MyAspNetCoreApp\\wwwroot", "Source": "MyAspNetCoreApp", "ContentRoot": "D:\\WebCuaTui\\CARRER GUIDE\\MyAspNetCoreApp\\wwwroot\\", "BasePath": "_content/MyAspNetCoreApp", "Pattern": "**"}], "Assets": [{"Identity": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\8.0.10\\staticwebassets\\V5\\css\\site.css", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\8.0.10\\staticwebassets\\V5\\", "BasePath": "Identity", "RelativePath": "css/site.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\8.0.10\\staticwebassets\\V5\\css\\site.css"}, {"Identity": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\8.0.10\\staticwebassets\\V5\\favicon.ico", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\8.0.10\\staticwebassets\\V5\\", "BasePath": "Identity", "RelativePath": "favicon.ico", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\8.0.10\\staticwebassets\\V5\\favicon.ico"}, {"Identity": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\8.0.10\\staticwebassets\\V5\\js\\site.js", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\8.0.10\\staticwebassets\\V5\\", "BasePath": "Identity", "RelativePath": "js/site.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\8.0.10\\staticwebassets\\V5\\js\\site.js"}, {"Identity": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\8.0.10\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\8.0.10\\staticwebassets\\V5\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\8.0.10\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css"}, {"Identity": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\8.0.10\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\8.0.10\\staticwebassets\\V5\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.css.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\8.0.10\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map"}, {"Identity": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\8.0.10\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\8.0.10\\staticwebassets\\V5\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.min.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\8.0.10\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css"}, {"Identity": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\8.0.10\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\8.0.10\\staticwebassets\\V5\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.min.css.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\8.0.10\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map"}, {"Identity": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\8.0.10\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\8.0.10\\staticwebassets\\V5\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\8.0.10\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css"}, {"Identity": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\8.0.10\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css.map", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\8.0.10\\staticwebassets\\V5\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\8.0.10\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css.map"}, {"Identity": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\8.0.10\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\8.0.10\\staticwebassets\\V5\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\8.0.10\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css"}, {"Identity": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\8.0.10\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css.map", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\8.0.10\\staticwebassets\\V5\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\8.0.10\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css.map"}, {"Identity": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\8.0.10\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\8.0.10\\staticwebassets\\V5\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\8.0.10\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css"}, {"Identity": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\8.0.10\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\8.0.10\\staticwebassets\\V5\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.css.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\8.0.10\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map"}, {"Identity": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\8.0.10\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\8.0.10\\staticwebassets\\V5\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.min.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\8.0.10\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css"}, {"Identity": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\8.0.10\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\8.0.10\\staticwebassets\\V5\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.min.css.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\8.0.10\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map"}, {"Identity": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\8.0.10\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\8.0.10\\staticwebassets\\V5\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\8.0.10\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css"}, {"Identity": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\8.0.10\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css.map", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\8.0.10\\staticwebassets\\V5\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\8.0.10\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css.map"}, {"Identity": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\8.0.10\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\8.0.10\\staticwebassets\\V5\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\8.0.10\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css"}, {"Identity": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\8.0.10\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css.map", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\8.0.10\\staticwebassets\\V5\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\8.0.10\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css.map"}, {"Identity": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\8.0.10\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\8.0.10\\staticwebassets\\V5\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\8.0.10\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css"}, {"Identity": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\8.0.10\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css.map", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\8.0.10\\staticwebassets\\V5\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.css.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\8.0.10\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css.map"}, {"Identity": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\8.0.10\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\8.0.10\\staticwebassets\\V5\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.min.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\8.0.10\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css"}, {"Identity": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\8.0.10\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css.map", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\8.0.10\\staticwebassets\\V5\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.min.css.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\8.0.10\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css.map"}, {"Identity": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\8.0.10\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\8.0.10\\staticwebassets\\V5\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\8.0.10\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css"}, {"Identity": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\8.0.10\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css.map", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\8.0.10\\staticwebassets\\V5\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\8.0.10\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css.map"}, {"Identity": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\8.0.10\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\8.0.10\\staticwebassets\\V5\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\8.0.10\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css"}, {"Identity": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\8.0.10\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css.map", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\8.0.10\\staticwebassets\\V5\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\8.0.10\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css.map"}, {"Identity": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\8.0.10\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.css", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\8.0.10\\staticwebassets\\V5\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\8.0.10\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.css"}, {"Identity": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\8.0.10\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.css.map", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\8.0.10\\staticwebassets\\V5\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap.css.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\8.0.10\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.css.map"}, {"Identity": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\8.0.10\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.min.css", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\8.0.10\\staticwebassets\\V5\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap.min.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\8.0.10\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.min.css"}, {"Identity": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\8.0.10\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.min.css.map", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\8.0.10\\staticwebassets\\V5\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap.min.css.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\8.0.10\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.min.css.map"}, {"Identity": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\8.0.10\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\8.0.10\\staticwebassets\\V5\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\8.0.10\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css"}, {"Identity": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\8.0.10\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css.map", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\8.0.10\\staticwebassets\\V5\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.css.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\8.0.10\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css.map"}, {"Identity": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\8.0.10\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\8.0.10\\staticwebassets\\V5\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.min.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\8.0.10\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css"}, {"Identity": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\8.0.10\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css.map", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\8.0.10\\staticwebassets\\V5\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.min.css.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\8.0.10\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css.map"}, {"Identity": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\8.0.10\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\8.0.10\\staticwebassets\\V5\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\8.0.10\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js"}, {"Identity": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\8.0.10\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\8.0.10\\staticwebassets\\V5\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.js.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\8.0.10\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map"}, {"Identity": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\8.0.10\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\8.0.10\\staticwebassets\\V5\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.min.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\8.0.10\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js"}, {"Identity": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\8.0.10\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\8.0.10\\staticwebassets\\V5\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.min.js.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\8.0.10\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map"}, {"Identity": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\8.0.10\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\8.0.10\\staticwebassets\\V5\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\8.0.10\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js"}, {"Identity": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\8.0.10\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js.map", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\8.0.10\\staticwebassets\\V5\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.js.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\8.0.10\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js.map"}, {"Identity": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\8.0.10\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\8.0.10\\staticwebassets\\V5\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.min.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\8.0.10\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js"}, {"Identity": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\8.0.10\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js.map", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\8.0.10\\staticwebassets\\V5\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.min.js.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\8.0.10\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js.map"}, {"Identity": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\8.0.10\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.js", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\8.0.10\\staticwebassets\\V5\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/js/bootstrap.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\8.0.10\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.js"}, {"Identity": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\8.0.10\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.js.map", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\8.0.10\\staticwebassets\\V5\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/js/bootstrap.js.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\8.0.10\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.js.map"}, {"Identity": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\8.0.10\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.min.js", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\8.0.10\\staticwebassets\\V5\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/js/bootstrap.min.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\8.0.10\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.min.js"}, {"Identity": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\8.0.10\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\8.0.10\\staticwebassets\\V5\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/js/bootstrap.min.js.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\8.0.10\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map"}, {"Identity": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\8.0.10\\staticwebassets\\V5\\lib\\bootstrap\\LICENSE", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\8.0.10\\staticwebassets\\V5\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/LICENSE", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\8.0.10\\staticwebassets\\V5\\lib\\bootstrap\\LICENSE"}, {"Identity": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\8.0.10\\staticwebassets\\V5\\lib\\jquery-validation-unobtrusive\\jquery.validate.unobtrusive.js", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\8.0.10\\staticwebassets\\V5\\", "BasePath": "Identity", "RelativePath": "lib/jquery-validation-unobtrusive/jquery.validate.unobtrusive.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\8.0.10\\staticwebassets\\V5\\lib\\jquery-validation-unobtrusive\\jquery.validate.unobtrusive.js"}, {"Identity": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\8.0.10\\staticwebassets\\V5\\lib\\jquery-validation-unobtrusive\\jquery.validate.unobtrusive.min.js", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\8.0.10\\staticwebassets\\V5\\", "BasePath": "Identity", "RelativePath": "lib/jquery-validation-unobtrusive/jquery.validate.unobtrusive.min.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\8.0.10\\staticwebassets\\V5\\lib\\jquery-validation-unobtrusive\\jquery.validate.unobtrusive.min.js"}, {"Identity": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\8.0.10\\staticwebassets\\V5\\lib\\jquery-validation-unobtrusive\\LICENSE.txt", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\8.0.10\\staticwebassets\\V5\\", "BasePath": "Identity", "RelativePath": "lib/jquery-validation-unobtrusive/LICENSE.txt", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\8.0.10\\staticwebassets\\V5\\lib\\jquery-validation-unobtrusive\\LICENSE.txt"}, {"Identity": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\8.0.10\\staticwebassets\\V5\\lib\\jquery-validation\\dist\\additional-methods.js", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\8.0.10\\staticwebassets\\V5\\", "BasePath": "Identity", "RelativePath": "lib/jquery-validation/dist/additional-methods.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\8.0.10\\staticwebassets\\V5\\lib\\jquery-validation\\dist\\additional-methods.js"}, {"Identity": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\8.0.10\\staticwebassets\\V5\\lib\\jquery-validation\\dist\\additional-methods.min.js", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\8.0.10\\staticwebassets\\V5\\", "BasePath": "Identity", "RelativePath": "lib/jquery-validation/dist/additional-methods.min.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\8.0.10\\staticwebassets\\V5\\lib\\jquery-validation\\dist\\additional-methods.min.js"}, {"Identity": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\8.0.10\\staticwebassets\\V5\\lib\\jquery-validation\\dist\\jquery.validate.js", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\8.0.10\\staticwebassets\\V5\\", "BasePath": "Identity", "RelativePath": "lib/jquery-validation/dist/jquery.validate.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\8.0.10\\staticwebassets\\V5\\lib\\jquery-validation\\dist\\jquery.validate.js"}, {"Identity": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\8.0.10\\staticwebassets\\V5\\lib\\jquery-validation\\dist\\jquery.validate.min.js", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\8.0.10\\staticwebassets\\V5\\", "BasePath": "Identity", "RelativePath": "lib/jquery-validation/dist/jquery.validate.min.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\8.0.10\\staticwebassets\\V5\\lib\\jquery-validation\\dist\\jquery.validate.min.js"}, {"Identity": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\8.0.10\\staticwebassets\\V5\\lib\\jquery-validation\\LICENSE.md", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\8.0.10\\staticwebassets\\V5\\", "BasePath": "Identity", "RelativePath": "lib/jquery-validation/LICENSE.md", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\8.0.10\\staticwebassets\\V5\\lib\\jquery-validation\\LICENSE.md"}, {"Identity": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\8.0.10\\staticwebassets\\V5\\lib\\jquery\\dist\\jquery.js", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\8.0.10\\staticwebassets\\V5\\", "BasePath": "Identity", "RelativePath": "lib/jquery/dist/jquery.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\8.0.10\\staticwebassets\\V5\\lib\\jquery\\dist\\jquery.js"}, {"Identity": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\8.0.10\\staticwebassets\\V5\\lib\\jquery\\dist\\jquery.min.js", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\8.0.10\\staticwebassets\\V5\\", "BasePath": "Identity", "RelativePath": "lib/jquery/dist/jquery.min.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\8.0.10\\staticwebassets\\V5\\lib\\jquery\\dist\\jquery.min.js"}, {"Identity": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\8.0.10\\staticwebassets\\V5\\lib\\jquery\\dist\\jquery.min.map", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\8.0.10\\staticwebassets\\V5\\", "BasePath": "Identity", "RelativePath": "lib/jquery/dist/jquery.min.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\8.0.10\\staticwebassets\\V5\\lib\\jquery\\dist\\jquery.min.map"}, {"Identity": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\8.0.10\\staticwebassets\\V5\\lib\\jquery\\LICENSE.txt", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\8.0.10\\staticwebassets\\V5\\", "BasePath": "Identity", "RelativePath": "lib/jquery/LICENSE.txt", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\8.0.10\\staticwebassets\\V5\\lib\\jquery\\LICENSE.txt"}, {"Identity": "D:\\WebCuaTui\\CARRER GUIDE\\MyAspNetCoreApp\\wwwroot\\css\\site.css", "SourceId": "MyAspNetCoreApp", "SourceType": "Discovered", "ContentRoot": "D:\\WebCuaTui\\CARRER GUIDE\\MyAspNetCoreApp\\wwwroot\\", "BasePath": "_content/MyAspNetCoreApp", "RelativePath": "css/site.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\site.css"}, {"Identity": "D:\\WebCuaTui\\CARRER GUIDE\\MyAspNetCoreApp\\wwwroot\\css\\style.css", "SourceId": "MyAspNetCoreApp", "SourceType": "Discovered", "ContentRoot": "D:\\WebCuaTui\\CARRER GUIDE\\MyAspNetCoreApp\\wwwroot\\", "BasePath": "_content/MyAspNetCoreApp", "RelativePath": "css/style.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\style.css"}, {"Identity": "D:\\WebCuaTui\\CARRER GUIDE\\MyAspNetCoreApp\\wwwroot\\favicon.ico", "SourceId": "MyAspNetCoreApp", "SourceType": "Discovered", "ContentRoot": "D:\\WebCuaTui\\CARRER GUIDE\\MyAspNetCoreApp\\wwwroot\\", "BasePath": "_content/MyAspNetCoreApp", "RelativePath": "favicon.ico", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\favicon.ico"}, {"Identity": "D:\\WebCuaTui\\CARRER GUIDE\\MyAspNetCoreApp\\wwwroot\\image\\bachkhoa.jpg", "SourceId": "MyAspNetCoreApp", "SourceType": "Discovered", "ContentRoot": "D:\\WebCuaTui\\CARRER GUIDE\\MyAspNetCoreApp\\wwwroot\\", "BasePath": "_content/MyAspNetCoreApp", "RelativePath": "image/bachkhoa.jpg", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\image\\bachkhoa.jpg"}, {"Identity": "D:\\WebCuaTui\\CARRER GUIDE\\MyAspNetCoreApp\\wwwroot\\image\\bia.png", "SourceId": "MyAspNetCoreApp", "SourceType": "Discovered", "ContentRoot": "D:\\WebCuaTui\\CARRER GUIDE\\MyAspNetCoreApp\\wwwroot\\", "BasePath": "_content/MyAspNetCoreApp", "RelativePath": "image/bia.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\image\\bia.png"}, {"Identity": "D:\\WebCuaTui\\CARRER GUIDE\\MyAspNetCoreApp\\wwwroot\\image\\FeatureHome.png", "SourceId": "MyAspNetCoreApp", "SourceType": "Discovered", "ContentRoot": "D:\\WebCuaTui\\CARRER GUIDE\\MyAspNetCoreApp\\wwwroot\\", "BasePath": "_content/MyAspNetCoreApp", "RelativePath": "image/FeatureHome.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\image\\FeatureHome.png"}, {"Identity": "D:\\WebCuaTui\\CARRER GUIDE\\MyAspNetCoreApp\\wwwroot\\image\\footerhome.jpg", "SourceId": "MyAspNetCoreApp", "SourceType": "Discovered", "ContentRoot": "D:\\WebCuaTui\\CARRER GUIDE\\MyAspNetCoreApp\\wwwroot\\", "BasePath": "_content/MyAspNetCoreApp", "RelativePath": "image/footerhome.jpg", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\image\\footerhome.jpg"}, {"Identity": "D:\\WebCuaTui\\CARRER GUIDE\\MyAspNetCoreApp\\wwwroot\\image\\homeeeeeeeeee.png", "SourceId": "MyAspNetCoreApp", "SourceType": "Discovered", "ContentRoot": "D:\\WebCuaTui\\CARRER GUIDE\\MyAspNetCoreApp\\wwwroot\\", "BasePath": "_content/MyAspNetCoreApp", "RelativePath": "image/homeeeeeeeeee.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\image\\homeeeeeeeeee.png"}, {"Identity": "D:\\WebCuaTui\\CARRER GUIDE\\MyAspNetCoreApp\\wwwroot\\image\\homesection.png", "SourceId": "MyAspNetCoreApp", "SourceType": "Discovered", "ContentRoot": "D:\\WebCuaTui\\CARRER GUIDE\\MyAspNetCoreApp\\wwwroot\\", "BasePath": "_content/MyAspNetCoreApp", "RelativePath": "image/homesection.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\image\\homesection.png"}, {"Identity": "D:\\WebCuaTui\\CARRER GUIDE\\MyAspNetCoreApp\\wwwroot\\image\\hutech.jpg", "SourceId": "MyAspNetCoreApp", "SourceType": "Discovered", "ContentRoot": "D:\\WebCuaTui\\CARRER GUIDE\\MyAspNetCoreApp\\wwwroot\\", "BasePath": "_content/MyAspNetCoreApp", "RelativePath": "image/hutech.jpg", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\image\\hutech.jpg"}, {"Identity": "D:\\WebCuaTui\\CARRER GUIDE\\MyAspNetCoreApp\\wwwroot\\image\\HUTECH2.jpg", "SourceId": "MyAspNetCoreApp", "SourceType": "Discovered", "ContentRoot": "D:\\WebCuaTui\\CARRER GUIDE\\MyAspNetCoreApp\\wwwroot\\", "BasePath": "_content/MyAspNetCoreApp", "RelativePath": "image/HUTECH2.jpg", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\image\\HUTECH2.jpg"}, {"Identity": "D:\\WebCuaTui\\CARRER GUIDE\\MyAspNetCoreApp\\wwwroot\\image\\logo.png", "SourceId": "MyAspNetCoreApp", "SourceType": "Discovered", "ContentRoot": "D:\\WebCuaTui\\CARRER GUIDE\\MyAspNetCoreApp\\wwwroot\\", "BasePath": "_content/MyAspNetCoreApp", "RelativePath": "image/logo.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\image\\logo.png"}, {"Identity": "D:\\WebCuaTui\\CARRER GUIDE\\MyAspNetCoreApp\\wwwroot\\image\\nhanvan.png", "SourceId": "MyAspNetCoreApp", "SourceType": "Discovered", "ContentRoot": "D:\\WebCuaTui\\CARRER GUIDE\\MyAspNetCoreApp\\wwwroot\\", "BasePath": "_content/MyAspNetCoreApp", "RelativePath": "image/nhanvan.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\image\\nhanvan.png"}, {"Identity": "D:\\WebCuaTui\\CARRER GUIDE\\MyAspNetCoreApp\\wwwroot\\image\\studentssmile.png", "SourceId": "MyAspNetCoreApp", "SourceType": "Discovered", "ContentRoot": "D:\\WebCuaTui\\CARRER GUIDE\\MyAspNetCoreApp\\wwwroot\\", "BasePath": "_content/MyAspNetCoreApp", "RelativePath": "image/studentssmile.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\image\\studentssmile.png"}, {"Identity": "D:\\WebCuaTui\\CARRER GUIDE\\MyAspNetCoreApp\\wwwroot\\js\\script.js", "SourceId": "MyAspNetCoreApp", "SourceType": "Discovered", "ContentRoot": "D:\\WebCuaTui\\CARRER GUIDE\\MyAspNetCoreApp\\wwwroot\\", "BasePath": "_content/MyAspNetCoreApp", "RelativePath": "js/script.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\js\\script.js"}, {"Identity": "D:\\WebCuaTui\\CARRER GUIDE\\MyAspNetCoreApp\\wwwroot\\js\\site.js", "SourceId": "MyAspNetCoreApp", "SourceType": "Discovered", "ContentRoot": "D:\\WebCuaTui\\CARRER GUIDE\\MyAspNetCoreApp\\wwwroot\\", "BasePath": "_content/MyAspNetCoreApp", "RelativePath": "js/site.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\js\\site.js"}, {"Identity": "D:\\WebCuaTui\\CARRER GUIDE\\MyAspNetCoreApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css", "SourceId": "MyAspNetCoreApp", "SourceType": "Discovered", "ContentRoot": "D:\\WebCuaTui\\CARRER GUIDE\\MyAspNetCoreApp\\wwwroot\\", "BasePath": "_content/MyAspNetCoreApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css"}, {"Identity": "D:\\WebCuaTui\\CARRER GUIDE\\MyAspNetCoreApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map", "SourceId": "MyAspNetCoreApp", "SourceType": "Discovered", "ContentRoot": "D:\\WebCuaTui\\CARRER GUIDE\\MyAspNetCoreApp\\wwwroot\\", "BasePath": "_content/MyAspNetCoreApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.css.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map"}, {"Identity": "D:\\WebCuaTui\\CARRER GUIDE\\MyAspNetCoreApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css", "SourceId": "MyAspNetCoreApp", "SourceType": "Discovered", "ContentRoot": "D:\\WebCuaTui\\CARRER GUIDE\\MyAspNetCoreApp\\wwwroot\\", "BasePath": "_content/MyAspNetCoreApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.min.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css"}, {"Identity": "D:\\WebCuaTui\\CARRER GUIDE\\MyAspNetCoreApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map", "SourceId": "MyAspNetCoreApp", "SourceType": "Discovered", "ContentRoot": "D:\\WebCuaTui\\CARRER GUIDE\\MyAspNetCoreApp\\wwwroot\\", "BasePath": "_content/MyAspNetCoreApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.min.css.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map"}, {"Identity": "D:\\WebCuaTui\\CARRER GUIDE\\MyAspNetCoreApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css", "SourceId": "MyAspNetCoreApp", "SourceType": "Discovered", "ContentRoot": "D:\\WebCuaTui\\CARRER GUIDE\\MyAspNetCoreApp\\wwwroot\\", "BasePath": "_content/MyAspNetCoreApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css"}, {"Identity": "D:\\WebCuaTui\\CARRER GUIDE\\MyAspNetCoreApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css.map", "SourceId": "MyAspNetCoreApp", "SourceType": "Discovered", "ContentRoot": "D:\\WebCuaTui\\CARRER GUIDE\\MyAspNetCoreApp\\wwwroot\\", "BasePath": "_content/MyAspNetCoreApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css.map"}, {"Identity": "D:\\WebCuaTui\\CARRER GUIDE\\MyAspNetCoreApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css", "SourceId": "MyAspNetCoreApp", "SourceType": "Discovered", "ContentRoot": "D:\\WebCuaTui\\CARRER GUIDE\\MyAspNetCoreApp\\wwwroot\\", "BasePath": "_content/MyAspNetCoreApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css"}, {"Identity": "D:\\WebCuaTui\\CARRER GUIDE\\MyAspNetCoreApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css.map", "SourceId": "MyAspNetCoreApp", "SourceType": "Discovered", "ContentRoot": "D:\\WebCuaTui\\CARRER GUIDE\\MyAspNetCoreApp\\wwwroot\\", "BasePath": "_content/MyAspNetCoreApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css.map"}, {"Identity": "D:\\WebCuaTui\\CARRER GUIDE\\MyAspNetCoreApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css", "SourceId": "MyAspNetCoreApp", "SourceType": "Discovered", "ContentRoot": "D:\\WebCuaTui\\CARRER GUIDE\\MyAspNetCoreApp\\wwwroot\\", "BasePath": "_content/MyAspNetCoreApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css"}, {"Identity": "D:\\WebCuaTui\\CARRER GUIDE\\MyAspNetCoreApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map", "SourceId": "MyAspNetCoreApp", "SourceType": "Discovered", "ContentRoot": "D:\\WebCuaTui\\CARRER GUIDE\\MyAspNetCoreApp\\wwwroot\\", "BasePath": "_content/MyAspNetCoreApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.css.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map"}, {"Identity": "D:\\WebCuaTui\\CARRER GUIDE\\MyAspNetCoreApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css", "SourceId": "MyAspNetCoreApp", "SourceType": "Discovered", "ContentRoot": "D:\\WebCuaTui\\CARRER GUIDE\\MyAspNetCoreApp\\wwwroot\\", "BasePath": "_content/MyAspNetCoreApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.min.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css"}, {"Identity": "D:\\WebCuaTui\\CARRER GUIDE\\MyAspNetCoreApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map", "SourceId": "MyAspNetCoreApp", "SourceType": "Discovered", "ContentRoot": "D:\\WebCuaTui\\CARRER GUIDE\\MyAspNetCoreApp\\wwwroot\\", "BasePath": "_content/MyAspNetCoreApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.min.css.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map"}, {"Identity": "D:\\WebCuaTui\\CARRER GUIDE\\MyAspNetCoreApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css", "SourceId": "MyAspNetCoreApp", "SourceType": "Discovered", "ContentRoot": "D:\\WebCuaTui\\CARRER GUIDE\\MyAspNetCoreApp\\wwwroot\\", "BasePath": "_content/MyAspNetCoreApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css"}, {"Identity": "D:\\WebCuaTui\\CARRER GUIDE\\MyAspNetCoreApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css.map", "SourceId": "MyAspNetCoreApp", "SourceType": "Discovered", "ContentRoot": "D:\\WebCuaTui\\CARRER GUIDE\\MyAspNetCoreApp\\wwwroot\\", "BasePath": "_content/MyAspNetCoreApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css.map"}, {"Identity": "D:\\WebCuaTui\\CARRER GUIDE\\MyAspNetCoreApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css", "SourceId": "MyAspNetCoreApp", "SourceType": "Discovered", "ContentRoot": "D:\\WebCuaTui\\CARRER GUIDE\\MyAspNetCoreApp\\wwwroot\\", "BasePath": "_content/MyAspNetCoreApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css"}, {"Identity": "D:\\WebCuaTui\\CARRER GUIDE\\MyAspNetCoreApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css.map", "SourceId": "MyAspNetCoreApp", "SourceType": "Discovered", "ContentRoot": "D:\\WebCuaTui\\CARRER GUIDE\\MyAspNetCoreApp\\wwwroot\\", "BasePath": "_content/MyAspNetCoreApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css.map"}, {"Identity": "D:\\WebCuaTui\\CARRER GUIDE\\MyAspNetCoreApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css", "SourceId": "MyAspNetCoreApp", "SourceType": "Discovered", "ContentRoot": "D:\\WebCuaTui\\CARRER GUIDE\\MyAspNetCoreApp\\wwwroot\\", "BasePath": "_content/MyAspNetCoreApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css"}, {"Identity": "D:\\WebCuaTui\\CARRER GUIDE\\MyAspNetCoreApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css.map", "SourceId": "MyAspNetCoreApp", "SourceType": "Discovered", "ContentRoot": "D:\\WebCuaTui\\CARRER GUIDE\\MyAspNetCoreApp\\wwwroot\\", "BasePath": "_content/MyAspNetCoreApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.css.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css.map"}, {"Identity": "D:\\WebCuaTui\\CARRER GUIDE\\MyAspNetCoreApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css", "SourceId": "MyAspNetCoreApp", "SourceType": "Discovered", "ContentRoot": "D:\\WebCuaTui\\CARRER GUIDE\\MyAspNetCoreApp\\wwwroot\\", "BasePath": "_content/MyAspNetCoreApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.min.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css"}, {"Identity": "D:\\WebCuaTui\\CARRER GUIDE\\MyAspNetCoreApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css.map", "SourceId": "MyAspNetCoreApp", "SourceType": "Discovered", "ContentRoot": "D:\\WebCuaTui\\CARRER GUIDE\\MyAspNetCoreApp\\wwwroot\\", "BasePath": "_content/MyAspNetCoreApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.min.css.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css.map"}, {"Identity": "D:\\WebCuaTui\\CARRER GUIDE\\MyAspNetCoreApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css", "SourceId": "MyAspNetCoreApp", "SourceType": "Discovered", "ContentRoot": "D:\\WebCuaTui\\CARRER GUIDE\\MyAspNetCoreApp\\wwwroot\\", "BasePath": "_content/MyAspNetCoreApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css"}, {"Identity": "D:\\WebCuaTui\\CARRER GUIDE\\MyAspNetCoreApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css.map", "SourceId": "MyAspNetCoreApp", "SourceType": "Discovered", "ContentRoot": "D:\\WebCuaTui\\CARRER GUIDE\\MyAspNetCoreApp\\wwwroot\\", "BasePath": "_content/MyAspNetCoreApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css.map"}, {"Identity": "D:\\WebCuaTui\\CARRER GUIDE\\MyAspNetCoreApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css", "SourceId": "MyAspNetCoreApp", "SourceType": "Discovered", "ContentRoot": "D:\\WebCuaTui\\CARRER GUIDE\\MyAspNetCoreApp\\wwwroot\\", "BasePath": "_content/MyAspNetCoreApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css"}, {"Identity": "D:\\WebCuaTui\\CARRER GUIDE\\MyAspNetCoreApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css.map", "SourceId": "MyAspNetCoreApp", "SourceType": "Discovered", "ContentRoot": "D:\\WebCuaTui\\CARRER GUIDE\\MyAspNetCoreApp\\wwwroot\\", "BasePath": "_content/MyAspNetCoreApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css.map"}, {"Identity": "D:\\WebCuaTui\\CARRER GUIDE\\MyAspNetCoreApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css", "SourceId": "MyAspNetCoreApp", "SourceType": "Discovered", "ContentRoot": "D:\\WebCuaTui\\CARRER GUIDE\\MyAspNetCoreApp\\wwwroot\\", "BasePath": "_content/MyAspNetCoreApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css"}, {"Identity": "D:\\WebCuaTui\\CARRER GUIDE\\MyAspNetCoreApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css.map", "SourceId": "MyAspNetCoreApp", "SourceType": "Discovered", "ContentRoot": "D:\\WebCuaTui\\CARRER GUIDE\\MyAspNetCoreApp\\wwwroot\\", "BasePath": "_content/MyAspNetCoreApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap.css.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css.map"}, {"Identity": "D:\\WebCuaTui\\CARRER GUIDE\\MyAspNetCoreApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css", "SourceId": "MyAspNetCoreApp", "SourceType": "Discovered", "ContentRoot": "D:\\WebCuaTui\\CARRER GUIDE\\MyAspNetCoreApp\\wwwroot\\", "BasePath": "_content/MyAspNetCoreApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap.min.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css"}, {"Identity": "D:\\WebCuaTui\\CARRER GUIDE\\MyAspNetCoreApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css.map", "SourceId": "MyAspNetCoreApp", "SourceType": "Discovered", "ContentRoot": "D:\\WebCuaTui\\CARRER GUIDE\\MyAspNetCoreApp\\wwwroot\\", "BasePath": "_content/MyAspNetCoreApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap.min.css.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css.map"}, {"Identity": "D:\\WebCuaTui\\CARRER GUIDE\\MyAspNetCoreApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css", "SourceId": "MyAspNetCoreApp", "SourceType": "Discovered", "ContentRoot": "D:\\WebCuaTui\\CARRER GUIDE\\MyAspNetCoreApp\\wwwroot\\", "BasePath": "_content/MyAspNetCoreApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css"}, {"Identity": "D:\\WebCuaTui\\CARRER GUIDE\\MyAspNetCoreApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css.map", "SourceId": "MyAspNetCoreApp", "SourceType": "Discovered", "ContentRoot": "D:\\WebCuaTui\\CARRER GUIDE\\MyAspNetCoreApp\\wwwroot\\", "BasePath": "_content/MyAspNetCoreApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.css.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css.map"}, {"Identity": "D:\\WebCuaTui\\CARRER GUIDE\\MyAspNetCoreApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css", "SourceId": "MyAspNetCoreApp", "SourceType": "Discovered", "ContentRoot": "D:\\WebCuaTui\\CARRER GUIDE\\MyAspNetCoreApp\\wwwroot\\", "BasePath": "_content/MyAspNetCoreApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.min.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css"}, {"Identity": "D:\\WebCuaTui\\CARRER GUIDE\\MyAspNetCoreApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css.map", "SourceId": "MyAspNetCoreApp", "SourceType": "Discovered", "ContentRoot": "D:\\WebCuaTui\\CARRER GUIDE\\MyAspNetCoreApp\\wwwroot\\", "BasePath": "_content/MyAspNetCoreApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.min.css.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css.map"}, {"Identity": "D:\\WebCuaTui\\CARRER GUIDE\\MyAspNetCoreApp\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js", "SourceId": "MyAspNetCoreApp", "SourceType": "Discovered", "ContentRoot": "D:\\WebCuaTui\\CARRER GUIDE\\MyAspNetCoreApp\\wwwroot\\", "BasePath": "_content/MyAspNetCoreApp", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js"}, {"Identity": "D:\\WebCuaTui\\CARRER GUIDE\\MyAspNetCoreApp\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map", "SourceId": "MyAspNetCoreApp", "SourceType": "Discovered", "ContentRoot": "D:\\WebCuaTui\\CARRER GUIDE\\MyAspNetCoreApp\\wwwroot\\", "BasePath": "_content/MyAspNetCoreApp", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.js.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map"}, {"Identity": "D:\\WebCuaTui\\CARRER GUIDE\\MyAspNetCoreApp\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js", "SourceId": "MyAspNetCoreApp", "SourceType": "Discovered", "ContentRoot": "D:\\WebCuaTui\\CARRER GUIDE\\MyAspNetCoreApp\\wwwroot\\", "BasePath": "_content/MyAspNetCoreApp", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.min.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js"}, {"Identity": "D:\\WebCuaTui\\CARRER GUIDE\\MyAspNetCoreApp\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map", "SourceId": "MyAspNetCoreApp", "SourceType": "Discovered", "ContentRoot": "D:\\WebCuaTui\\CARRER GUIDE\\MyAspNetCoreApp\\wwwroot\\", "BasePath": "_content/MyAspNetCoreApp", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.min.js.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map"}, {"Identity": "D:\\WebCuaTui\\CARRER GUIDE\\MyAspNetCoreApp\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js", "SourceId": "MyAspNetCoreApp", "SourceType": "Discovered", "ContentRoot": "D:\\WebCuaTui\\CARRER GUIDE\\MyAspNetCoreApp\\wwwroot\\", "BasePath": "_content/MyAspNetCoreApp", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js"}, {"Identity": "D:\\WebCuaTui\\CARRER GUIDE\\MyAspNetCoreApp\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js.map", "SourceId": "MyAspNetCoreApp", "SourceType": "Discovered", "ContentRoot": "D:\\WebCuaTui\\CARRER GUIDE\\MyAspNetCoreApp\\wwwroot\\", "BasePath": "_content/MyAspNetCoreApp", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.js.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js.map"}, {"Identity": "D:\\WebCuaTui\\CARRER GUIDE\\MyAspNetCoreApp\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js", "SourceId": "MyAspNetCoreApp", "SourceType": "Discovered", "ContentRoot": "D:\\WebCuaTui\\CARRER GUIDE\\MyAspNetCoreApp\\wwwroot\\", "BasePath": "_content/MyAspNetCoreApp", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.min.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js"}, {"Identity": "D:\\WebCuaTui\\CARRER GUIDE\\MyAspNetCoreApp\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js.map", "SourceId": "MyAspNetCoreApp", "SourceType": "Discovered", "ContentRoot": "D:\\WebCuaTui\\CARRER GUIDE\\MyAspNetCoreApp\\wwwroot\\", "BasePath": "_content/MyAspNetCoreApp", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.min.js.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js.map"}, {"Identity": "D:\\WebCuaTui\\CARRER GUIDE\\MyAspNetCoreApp\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js", "SourceId": "MyAspNetCoreApp", "SourceType": "Discovered", "ContentRoot": "D:\\WebCuaTui\\CARRER GUIDE\\MyAspNetCoreApp\\wwwroot\\", "BasePath": "_content/MyAspNetCoreApp", "RelativePath": "lib/bootstrap/dist/js/bootstrap.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js"}, {"Identity": "D:\\WebCuaTui\\CARRER GUIDE\\MyAspNetCoreApp\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js.map", "SourceId": "MyAspNetCoreApp", "SourceType": "Discovered", "ContentRoot": "D:\\WebCuaTui\\CARRER GUIDE\\MyAspNetCoreApp\\wwwroot\\", "BasePath": "_content/MyAspNetCoreApp", "RelativePath": "lib/bootstrap/dist/js/bootstrap.js.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js.map"}, {"Identity": "D:\\WebCuaTui\\CARRER GUIDE\\MyAspNetCoreApp\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js", "SourceId": "MyAspNetCoreApp", "SourceType": "Discovered", "ContentRoot": "D:\\WebCuaTui\\CARRER GUIDE\\MyAspNetCoreApp\\wwwroot\\", "BasePath": "_content/MyAspNetCoreApp", "RelativePath": "lib/bootstrap/dist/js/bootstrap.min.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js"}, {"Identity": "D:\\WebCuaTui\\CARRER GUIDE\\MyAspNetCoreApp\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map", "SourceId": "MyAspNetCoreApp", "SourceType": "Discovered", "ContentRoot": "D:\\WebCuaTui\\CARRER GUIDE\\MyAspNetCoreApp\\wwwroot\\", "BasePath": "_content/MyAspNetCoreApp", "RelativePath": "lib/bootstrap/dist/js/bootstrap.min.js.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map"}, {"Identity": "D:\\WebCuaTui\\CARRER GUIDE\\MyAspNetCoreApp\\wwwroot\\lib\\bootstrap\\LICENSE", "SourceId": "MyAspNetCoreApp", "SourceType": "Discovered", "ContentRoot": "D:\\WebCuaTui\\CARRER GUIDE\\MyAspNetCoreApp\\wwwroot\\", "BasePath": "_content/MyAspNetCoreApp", "RelativePath": "lib/bootstrap/LICENSE", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\LICENSE"}, {"Identity": "D:\\WebCuaTui\\CARRER GUIDE\\MyAspNetCoreApp\\wwwroot\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.js", "SourceId": "MyAspNetCoreApp", "SourceType": "Discovered", "ContentRoot": "D:\\WebCuaTui\\CARRER GUIDE\\MyAspNetCoreApp\\wwwroot\\", "BasePath": "_content/MyAspNetCoreApp", "RelativePath": "lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.js"}, {"Identity": "D:\\WebCuaTui\\CARRER GUIDE\\MyAspNetCoreApp\\wwwroot\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.min.js", "SourceId": "MyAspNetCoreApp", "SourceType": "Discovered", "ContentRoot": "D:\\WebCuaTui\\CARRER GUIDE\\MyAspNetCoreApp\\wwwroot\\", "BasePath": "_content/MyAspNetCoreApp", "RelativePath": "lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive.min.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.min.js"}, {"Identity": "D:\\WebCuaTui\\CARRER GUIDE\\MyAspNetCoreApp\\wwwroot\\lib\\jquery-validation-unobtrusive\\LICENSE.txt", "SourceId": "MyAspNetCoreApp", "SourceType": "Discovered", "ContentRoot": "D:\\WebCuaTui\\CARRER GUIDE\\MyAspNetCoreApp\\wwwroot\\", "BasePath": "_content/MyAspNetCoreApp", "RelativePath": "lib/jquery-validation-unobtrusive/LICENSE.txt", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation-unobtrusive\\LICENSE.txt"}, {"Identity": "D:\\WebCuaTui\\CARRER GUIDE\\MyAspNetCoreApp\\wwwroot\\lib\\jquery-validation\\dist\\additional-methods.js", "SourceId": "MyAspNetCoreApp", "SourceType": "Discovered", "ContentRoot": "D:\\WebCuaTui\\CARRER GUIDE\\MyAspNetCoreApp\\wwwroot\\", "BasePath": "_content/MyAspNetCoreApp", "RelativePath": "lib/jquery-validation/dist/additional-methods.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation\\dist\\additional-methods.js"}, {"Identity": "D:\\WebCuaTui\\CARRER GUIDE\\MyAspNetCoreApp\\wwwroot\\lib\\jquery-validation\\dist\\additional-methods.min.js", "SourceId": "MyAspNetCoreApp", "SourceType": "Discovered", "ContentRoot": "D:\\WebCuaTui\\CARRER GUIDE\\MyAspNetCoreApp\\wwwroot\\", "BasePath": "_content/MyAspNetCoreApp", "RelativePath": "lib/jquery-validation/dist/additional-methods.min.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation\\dist\\additional-methods.min.js"}, {"Identity": "D:\\WebCuaTui\\CARRER GUIDE\\MyAspNetCoreApp\\wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.js", "SourceId": "MyAspNetCoreApp", "SourceType": "Discovered", "ContentRoot": "D:\\WebCuaTui\\CARRER GUIDE\\MyAspNetCoreApp\\wwwroot\\", "BasePath": "_content/MyAspNetCoreApp", "RelativePath": "lib/jquery-validation/dist/jquery.validate.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.js"}, {"Identity": "D:\\WebCuaTui\\CARRER GUIDE\\MyAspNetCoreApp\\wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.min.js", "SourceId": "MyAspNetCoreApp", "SourceType": "Discovered", "ContentRoot": "D:\\WebCuaTui\\CARRER GUIDE\\MyAspNetCoreApp\\wwwroot\\", "BasePath": "_content/MyAspNetCoreApp", "RelativePath": "lib/jquery-validation/dist/jquery.validate.min.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.min.js"}, {"Identity": "D:\\WebCuaTui\\CARRER GUIDE\\MyAspNetCoreApp\\wwwroot\\lib\\jquery-validation\\LICENSE.md", "SourceId": "MyAspNetCoreApp", "SourceType": "Discovered", "ContentRoot": "D:\\WebCuaTui\\CARRER GUIDE\\MyAspNetCoreApp\\wwwroot\\", "BasePath": "_content/MyAspNetCoreApp", "RelativePath": "lib/jquery-validation/LICENSE.md", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation\\LICENSE.md"}, {"Identity": "D:\\WebCuaTui\\CARRER GUIDE\\MyAspNetCoreApp\\wwwroot\\lib\\jquery\\dist\\jquery.js", "SourceId": "MyAspNetCoreApp", "SourceType": "Discovered", "ContentRoot": "D:\\WebCuaTui\\CARRER GUIDE\\MyAspNetCoreApp\\wwwroot\\", "BasePath": "_content/MyAspNetCoreApp", "RelativePath": "lib/jquery/dist/jquery.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery\\dist\\jquery.js"}, {"Identity": "D:\\WebCuaTui\\CARRER GUIDE\\MyAspNetCoreApp\\wwwroot\\lib\\jquery\\dist\\jquery.min.js", "SourceId": "MyAspNetCoreApp", "SourceType": "Discovered", "ContentRoot": "D:\\WebCuaTui\\CARRER GUIDE\\MyAspNetCoreApp\\wwwroot\\", "BasePath": "_content/MyAspNetCoreApp", "RelativePath": "lib/jquery/dist/jquery.min.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery\\dist\\jquery.min.js"}, {"Identity": "D:\\WebCuaTui\\CARRER GUIDE\\MyAspNetCoreApp\\wwwroot\\lib\\jquery\\dist\\jquery.min.map", "SourceId": "MyAspNetCoreApp", "SourceType": "Discovered", "ContentRoot": "D:\\WebCuaTui\\CARRER GUIDE\\MyAspNetCoreApp\\wwwroot\\", "BasePath": "_content/MyAspNetCoreApp", "RelativePath": "lib/jquery/dist/jquery.min.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery\\dist\\jquery.min.map"}, {"Identity": "D:\\WebCuaTui\\CARRER GUIDE\\MyAspNetCoreApp\\wwwroot\\lib\\jquery\\dist\\jquery.slim.js", "SourceId": "MyAspNetCoreApp", "SourceType": "Discovered", "ContentRoot": "D:\\WebCuaTui\\CARRER GUIDE\\MyAspNetCoreApp\\wwwroot\\", "BasePath": "_content/MyAspNetCoreApp", "RelativePath": "lib/jquery/dist/jquery.slim.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery\\dist\\jquery.slim.js"}, {"Identity": "D:\\WebCuaTui\\CARRER GUIDE\\MyAspNetCoreApp\\wwwroot\\lib\\jquery\\dist\\jquery.slim.min.js", "SourceId": "MyAspNetCoreApp", "SourceType": "Discovered", "ContentRoot": "D:\\WebCuaTui\\CARRER GUIDE\\MyAspNetCoreApp\\wwwroot\\", "BasePath": "_content/MyAspNetCoreApp", "RelativePath": "lib/jquery/dist/jquery.slim.min.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery\\dist\\jquery.slim.min.js"}, {"Identity": "D:\\WebCuaTui\\CARRER GUIDE\\MyAspNetCoreApp\\wwwroot\\lib\\jquery\\dist\\jquery.slim.min.map", "SourceId": "MyAspNetCoreApp", "SourceType": "Discovered", "ContentRoot": "D:\\WebCuaTui\\CARRER GUIDE\\MyAspNetCoreApp\\wwwroot\\", "BasePath": "_content/MyAspNetCoreApp", "RelativePath": "lib/jquery/dist/jquery.slim.min.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery\\dist\\jquery.slim.min.map"}, {"Identity": "D:\\WebCuaTui\\CARRER GUIDE\\MyAspNetCoreApp\\wwwroot\\lib\\jquery\\LICENSE.txt", "SourceId": "MyAspNetCoreApp", "SourceType": "Discovered", "ContentRoot": "D:\\WebCuaTui\\CARRER GUIDE\\MyAspNetCoreApp\\wwwroot\\", "BasePath": "_content/MyAspNetCoreApp", "RelativePath": "lib/jquery/LICENSE.txt", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery\\LICENSE.txt"}]}