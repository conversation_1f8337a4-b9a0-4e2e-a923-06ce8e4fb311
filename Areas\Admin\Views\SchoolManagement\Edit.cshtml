@model School

@{
    ViewData["Title"] = "Chỉnh sửa trường học";
    Layout = "~/Areas/Admin/Views/Shared/_Layout.cshtml";
}

<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h2 class="mb-1">
            <i class="bi bi-pencil-square text-warning"></i>
            Chỉnh sửa trường học
        </h2>
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a asp-action="Index" asp-controller="Dashboard">Dashboard</a></li>
                <li class="breadcrumb-item"><a asp-action="Index">Quản lý trường học</a></li>
                <li class="breadcrumb-item active">Chỉnh sửa</li>
            </ol>
        </nav>
    </div>
</div>

<div class="row">
    <div class="col-lg-8">
        <div class="card shadow-sm">
            <div class="card-header bg-warning text-white">
                <h5 class="mb-0">
                    <i class="bi bi-info-circle"></i>
                    Thông tin trường học
                </h5>
            </div>
            <div class="card-body">
                <form asp-action="Edit" method="post">
                    <input asp-for="Id" type="hidden" />
                    
                    <div class="row">
                        <div class="col-md-8 mb-3">
                            <label asp-for="Name" class="form-label"></label>
                            <input asp-for="Name" class="form-control" placeholder="Ví dụ: Đại học Bách khoa Hà Nội" />
                            <span asp-validation-for="Name" class="text-danger"></span>
                        </div>
                        <div class="col-md-4 mb-3">
                            <label asp-for="Type" class="form-label">Loại trường</label>
                            <select asp-for="Type" class="form-select">
                                <option value="">Chọn loại trường</option>
                                <option value="Công lập">Công lập</option>
                                <option value="Tư thục">Tư thục</option>
                                <option value="Dân lập">Dân lập</option>
                            </select>
                            <span asp-validation-for="Type" class="text-danger"></span>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label asp-for="Description" class="form-label"></label>
                        <textarea asp-for="Description" class="form-control" rows="4" 
                                  placeholder="Mô tả về trường học, các ngành đào tạo, thế mạnh..."></textarea>
                        <span asp-validation-for="Description" class="text-danger"></span>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-4 mb-3">
                            <label asp-for="Address" class="form-label">Địa chỉ</label>
                            <input asp-for="Address" class="form-control" placeholder="Ví dụ: 1 Đại Cồ Việt, Hà Nội" />
                            <span asp-validation-for="Address" class="text-danger"></span>
                        </div>
                        <div class="col-md-4 mb-3">
                            <label asp-for="PhoneNumber" class="form-label">Số điện thoại</label>
                            <input asp-for="PhoneNumber" class="form-control" placeholder="024-1234-5678" />
                            <span asp-validation-for="PhoneNumber" class="text-danger"></span>
                        </div>
                        <div class="col-md-4 mb-3">
                            <label asp-for="EstablishedYear" class="form-label"></label>
                            <input asp-for="EstablishedYear" type="number" class="form-control" 
                                   min="1900" max="@DateTime.Now.Year" placeholder="@DateTime.Now.Year" />
                            <span asp-validation-for="EstablishedYear" class="text-danger"></span>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label asp-for="Website" class="form-label"></label>
                            <input asp-for="Website" type="url" class="form-control" 
                                   placeholder="https://example.edu.vn" />
                            <span asp-validation-for="Website" class="text-danger"></span>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label asp-for="TuitionFee" class="form-label"></label>
                            <div class="input-group">
                                <input asp-for="TuitionFee" type="number" class="form-control" 
                                       placeholder="20000000" step="100000" />
                                <span class="input-group-text">VNĐ</span>
                            </div>
                            <span asp-validation-for="TuitionFee" class="text-danger"></span>
                            <div class="form-text">Học phí trung bình mỗi năm</div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label asp-for="ImageUrl" class="form-label"></label>
                        <input asp-for="ImageUrl" type="url" class="form-control" 
                               placeholder="https://example.com/image.jpg" />
                        <span asp-validation-for="ImageUrl" class="text-danger"></span>
                        <div class="form-text">URL hình ảnh đại diện cho trường học</div>
                    </div>
                    
                    <div class="d-flex justify-content-between">
                        <a asp-action="Index" class="btn btn-secondary">
                            <i class="bi bi-arrow-left"></i> Quay lại
                        </a>
                        <button type="submit" class="btn btn-warning">
                            <i class="bi bi-check-circle"></i> Cập nhật trường học
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="bi bi-eye"></i> Xem trước
                </h6>
            </div>
            <div class="card-body">
                <div id="preview">
                    <div class="card">
                        @if (!string.IsNullOrEmpty(Model.ImageUrl))
                        {
                            <img src="@Model.ImageUrl" class="card-img-top" style="height: 120px; object-fit: cover;" />
                        }
                        <div class="card-body">
                            <h6 class="card-title">@Model.Name</h6>
                            <p class="card-text">
                                <small class="text-muted">
                                    <i class="bi bi-geo-alt"></i> @Model.Address
                                </small>
                            </p>
                            @if (!string.IsNullOrEmpty(Model.Type))
                            {
                                <span class="badge bg-@(Model.Type == "Công lập" ? "success" : Model.Type == "Tư thục" ? "info" : "warning")">
                                    @Model.Type
                                </span>
                            }
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="card mt-3">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="bi bi-info-circle"></i> Thông tin
                </h6>
            </div>
            <div class="card-body">
                <p><strong>ID:</strong> #@Model.Id</p>
                <p><strong>Năm thành lập:</strong> @Model.EstablishedYear</p>
                @if (Model.TuitionFee > 0)
                {
                    <p><strong>Học phí:</strong> @Model.TuitionFee.ToString("N0") VNĐ</p>
                }
                @if (!string.IsNullOrEmpty(Model.Website))
                {
                    <p><strong>Website:</strong> <a href="@Model.Website" target="_blank">@Model.Website</a></p>
                }
            </div>
        </div>
    </div>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
    
    <script>
        // Live preview functionality
        function updatePreview() {
            const name = document.getElementById('Name').value || '@Model.Name';
            const type = document.getElementById('Type').value || '@Model.Type';
            const address = document.getElementById('Address').value || '@Model.Address';
            const imageUrl = document.getElementById('ImageUrl').value || '@Model.ImageUrl';
            
            let preview = '<div class="card">';
            
            if (imageUrl) {
                preview += `<img src="${imageUrl}" class="card-img-top" style="height: 120px; object-fit: cover;" onerror="this.style.display='none'">`;
            }
            
            preview += '<div class="card-body">';
            preview += `<h6 class="card-title">${name}</h6>`;
            preview += `<p class="card-text"><small class="text-muted"><i class="bi bi-geo-alt"></i> ${address}</small></p>`;
            
            if (type) {
                const badgeClass = type === 'Công lập' ? 'success' : type === 'Tư thục' ? 'info' : 'warning';
                preview += `<span class="badge bg-${badgeClass}">${type}</span>`;
            }
            
            preview += '</div></div>';
            
            document.getElementById('preview').innerHTML = preview;
        }
        
        // Add event listeners
        ['Name', 'Type', 'Address', 'ImageUrl'].forEach(id => {
            const element = document.getElementById(id);
            if (element) {
                element.addEventListener('input', updatePreview);
            }
        });
    </script>
}
