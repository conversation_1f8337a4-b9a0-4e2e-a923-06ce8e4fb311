@model Survey
@{
    ViewData["Title"] = $"Khảo sát: {Model.Title}";
}

<div class="row">
    <div class="col-12">
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a asp-controller="Home" asp-action="Index">Trang chủ</a></li>
                <li class="breadcrumb-item"><a asp-action="Index">Khảo sát</a></li>
                <li class="breadcrumb-item active">@Model.Title</li>
            </ol>
        </nav>
    </div>
</div>

<div class="row">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header bg-primary text-white">
                <h4 class="mb-0">@Model.Title</h4>
                <p class="mb-0 mt-2">@Model.Description</p>
            </div>
            <div class="card-body">
                <form asp-action="Submit" method="post" id="surveyForm">
                    <input type="hidden" name="surveyId" value="@Model.Id" />
                    
                    @{
                        var questionsList = Model.Questions.ToList();
                    }
                    @for (int i = 0; i < questionsList.Count; i++)
                    {
                        var question = questionsList[i];
                        <div class="question-container mb-4 p-3 border rounded">
                            <h5 class="question-title">
                                Câu @(i + 1): @question.QuestionText
                                @if (!string.IsNullOrEmpty(question.Category))
                                {
                                    <span class="badge bg-secondary">@question.Category</span>
                                }
                            </h5>
                            
                            <div class="answers mt-3">
                                @foreach (var answer in question.Answers)
                                {
                                    <div class="form-check mb-2">
                                        <input class="form-check-input" type="radio" 
                                               name="answers[@question.Id]" 
                                               value="@answer.Id" 
                                               id="<EMAIL>"
                                               required>
                                        <label class="form-check-label" for="<EMAIL>">
                                            @answer.AnswerText
                                        </label>
                                    </div>
                                }
                            </div>
                        </div>
                    }
                    
                    <div class="text-center mt-4">
                        <button type="submit" class="btn btn-success btn-lg" onclick="return confirmSubmit()">
                            <i class="bi bi-check-circle"></i> Hoàn thành khảo sát
                        </button>
                        <a asp-action="Index" class="btn btn-secondary btn-lg ms-2">
                            <i class="bi bi-arrow-left"></i> Quay lại
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-lg-4">
        <div class="card sticky-top">
            <div class="card-header">
                <h5 class="mb-0">Thông tin khảo sát</h5>
            </div>
            <div class="card-body">
                <ul class="list-unstyled">
                    <li><strong>Loại:</strong> @Model.Type.ToString()</li>
                    <li><strong>Số câu hỏi:</strong> @Model.Questions.Count</li>
                    <li><strong>Thời gian:</strong> Không giới hạn</li>
                    <li><strong>Ngày tạo:</strong> @Model.CreatedDate.ToString("dd/MM/yyyy")</li>
                </ul>
                
                <div class="progress mb-3">
                    <div class="progress-bar bg-success" role="progressbar" style="width: 0%" id="progress-bar">
                        0%
                    </div>
                </div>
                
                <div class="alert alert-info">
                    <small>
                        <i class="bi bi-info-circle"></i>
                        Hãy trả lời thật lòng để có kết quả chính xác nhất.
                    </small>
                </div>
                
                @if (Model.Type == SurveyType.MBTI)
                {
                    <div class="alert alert-warning">
                        <small>
                            <strong>Lưu ý:</strong> Không có câu trả lời đúng hay sai. 
                            Hãy chọn đáp án phù hợp nhất với bạn.
                        </small>
                    </div>
                }
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        // Progress tracking
        function updateProgress() {
            const totalQuestions = @Model.Questions.Count;
            const answeredQuestions = document.querySelectorAll('input[type="radio"]:checked').length;
            const progress = (answeredQuestions / totalQuestions) * 100;
            
            document.getElementById('progress-bar').style.width = progress + '%';
            document.getElementById('progress-bar').textContent = Math.round(progress) + '%';
        }
        
        // Add event listeners to radio buttons
        document.querySelectorAll('input[type="radio"]').forEach(radio => {
            radio.addEventListener('change', updateProgress);
        });
        
        function confirmSubmit() {
            const totalQuestions = @Model.Questions.Count;
            const answeredQuestions = document.querySelectorAll('input[type="radio"]:checked').length;
            
            if (answeredQuestions < totalQuestions) {
                alert(`Bạn chỉ trả lời ${answeredQuestions}/${totalQuestions} câu hỏi. Vui lòng hoàn thành tất cả câu hỏi.`);
                return false;
            }
            
            return confirm('Bạn có chắc muốn hoàn thành khảo sát? Sau khi hoàn thành sẽ không thể chỉnh sửa.');
        }
        
        // Auto-save functionality (optional)
        let autoSaveTimer;
        document.querySelectorAll('input[type="radio"]').forEach(radio => {
            radio.addEventListener('change', function() {
                clearTimeout(autoSaveTimer);
                autoSaveTimer = setTimeout(function() {
                    // Could implement auto-save here
                    console.log('Auto-save triggered');
                }, 2000);
            });
        });
    </script>
}
