﻿// <auto-generated />
using System;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Metadata;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;

#nullable disable

namespace MyAspNetCoreApp.Migrations
{
    [DbContext(typeof(CareerGuideDbContext))]
    [Migration("20250608090436_AddSchoolType")]
    partial class AddSchoolType
    {
        /// <inheritdoc />
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "8.0.10")
                .HasAnnotation("Relational:MaxIdentifierLength", 128);

            SqlServerModelBuilderExtensions.UseIdentityColumns(modelBuilder);

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityRole", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("ConcurrencyStamp")
                        .IsConcurrencyToken()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Name")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<string>("NormalizedName")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.HasKey("Id");

                    b.HasIndex("NormalizedName")
                        .IsUnique()
                        .HasDatabaseName("RoleNameIndex")
                        .HasFilter("[NormalizedName] IS NOT NULL");

                    b.ToTable("AspNetRoles", (string)null);
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityRoleClaim<string>", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("ClaimType")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ClaimValue")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("RoleId")
                        .IsRequired()
                        .HasColumnType("nvarchar(450)");

                    b.HasKey("Id");

                    b.HasIndex("RoleId");

                    b.ToTable("AspNetRoleClaims", (string)null);
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUser", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("nvarchar(450)");

                    b.Property<int>("AccessFailedCount")
                        .HasColumnType("int");

                    b.Property<string>("ConcurrencyStamp")
                        .IsConcurrencyToken()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Email")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<bool>("EmailConfirmed")
                        .HasColumnType("bit");

                    b.Property<bool>("LockoutEnabled")
                        .HasColumnType("bit");

                    b.Property<DateTimeOffset?>("LockoutEnd")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("NormalizedEmail")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<string>("NormalizedUserName")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<string>("PasswordHash")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("PhoneNumber")
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("PhoneNumberConfirmed")
                        .HasColumnType("bit");

                    b.Property<string>("SecurityStamp")
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("TwoFactorEnabled")
                        .HasColumnType("bit");

                    b.Property<string>("UserName")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.HasKey("Id");

                    b.HasIndex("NormalizedEmail")
                        .HasDatabaseName("EmailIndex");

                    b.HasIndex("NormalizedUserName")
                        .IsUnique()
                        .HasDatabaseName("UserNameIndex")
                        .HasFilter("[NormalizedUserName] IS NOT NULL");

                    b.ToTable("AspNetUsers", (string)null);
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserClaim<string>", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("ClaimType")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ClaimValue")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("UserId")
                        .IsRequired()
                        .HasColumnType("nvarchar(450)");

                    b.HasKey("Id");

                    b.HasIndex("UserId");

                    b.ToTable("AspNetUserClaims", (string)null);
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserLogin<string>", b =>
                {
                    b.Property<string>("LoginProvider")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("ProviderKey")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("ProviderDisplayName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("UserId")
                        .IsRequired()
                        .HasColumnType("nvarchar(450)");

                    b.HasKey("LoginProvider", "ProviderKey");

                    b.HasIndex("UserId");

                    b.ToTable("AspNetUserLogins", (string)null);
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserRole<string>", b =>
                {
                    b.Property<string>("UserId")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("RoleId")
                        .HasColumnType("nvarchar(450)");

                    b.HasKey("UserId", "RoleId");

                    b.HasIndex("RoleId");

                    b.ToTable("AspNetUserRoles", (string)null);
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserToken<string>", b =>
                {
                    b.Property<string>("UserId")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("LoginProvider")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("Name")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("Value")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("UserId", "LoginProvider", "Name");

                    b.ToTable("AspNetUserTokens", (string)null);
                });

            modelBuilder.Entity("MyAspNetCoreApp.Models.Answer", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("AnswerText")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.Property<bool>("IsCorrect")
                        .HasColumnType("bit");

                    b.Property<int>("OrderNumber")
                        .HasColumnType("int");

                    b.Property<int>("QuestionId")
                        .HasColumnType("int");

                    b.Property<string>("Text")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.HasIndex("QuestionId");

                    b.ToTable("Answers");

                    b.HasData(
                        new
                        {
                            Id = 1,
                            AnswerText = "f'(x) = 2x + 3",
                            IsCorrect = true,
                            OrderNumber = 1,
                            QuestionId = 1,
                            Text = "f'(x) = 2x + 3"
                        },
                        new
                        {
                            Id = 2,
                            AnswerText = "f'(x) = x² + 3",
                            IsCorrect = false,
                            OrderNumber = 2,
                            QuestionId = 1,
                            Text = "f'(x) = x² + 3"
                        },
                        new
                        {
                            Id = 3,
                            AnswerText = "f'(x) = 2x - 2",
                            IsCorrect = false,
                            OrderNumber = 3,
                            QuestionId = 1,
                            Text = "f'(x) = 2x - 2"
                        },
                        new
                        {
                            Id = 4,
                            AnswerText = "f'(x) = 2x + 5",
                            IsCorrect = false,
                            OrderNumber = 4,
                            QuestionId = 1,
                            Text = "f'(x) = 2x + 5"
                        },
                        new
                        {
                            Id = 5,
                            AnswerText = "x = 4",
                            IsCorrect = true,
                            OrderNumber = 1,
                            QuestionId = 2,
                            Text = "x = 4"
                        },
                        new
                        {
                            Id = 6,
                            AnswerText = "x = 3",
                            IsCorrect = false,
                            OrderNumber = 2,
                            QuestionId = 2,
                            Text = "x = 3"
                        },
                        new
                        {
                            Id = 7,
                            AnswerText = "x = 5",
                            IsCorrect = false,
                            OrderNumber = 3,
                            QuestionId = 2,
                            Text = "x = 5"
                        },
                        new
                        {
                            Id = 8,
                            AnswerText = "x = 6",
                            IsCorrect = false,
                            OrderNumber = 4,
                            QuestionId = 2,
                            Text = "x = 6"
                        },
                        new
                        {
                            Id = 9,
                            AnswerText = "6",
                            IsCorrect = true,
                            OrderNumber = 1,
                            QuestionId = 3,
                            Text = "6"
                        },
                        new
                        {
                            Id = 10,
                            AnswerText = "4",
                            IsCorrect = false,
                            OrderNumber = 2,
                            QuestionId = 3,
                            Text = "4"
                        },
                        new
                        {
                            Id = 11,
                            AnswerText = "8",
                            IsCorrect = false,
                            OrderNumber = 3,
                            QuestionId = 3,
                            Text = "8"
                        },
                        new
                        {
                            Id = 12,
                            AnswerText = "5",
                            IsCorrect = false,
                            OrderNumber = 4,
                            QuestionId = 3,
                            Text = "5"
                        },
                        new
                        {
                            Id = 13,
                            AnswerText = "go",
                            IsCorrect = true,
                            OrderNumber = 1,
                            QuestionId = 4,
                            Text = "go"
                        },
                        new
                        {
                            Id = 14,
                            AnswerText = "goes",
                            IsCorrect = false,
                            OrderNumber = 2,
                            QuestionId = 4,
                            Text = "goes"
                        },
                        new
                        {
                            Id = 15,
                            AnswerText = "going",
                            IsCorrect = false,
                            OrderNumber = 3,
                            QuestionId = 4,
                            Text = "going"
                        },
                        new
                        {
                            Id = 16,
                            AnswerText = "went",
                            IsCorrect = false,
                            OrderNumber = 4,
                            QuestionId = 4,
                            Text = "went"
                        },
                        new
                        {
                            Id = 17,
                            AnswerText = "went",
                            IsCorrect = true,
                            OrderNumber = 1,
                            QuestionId = 5,
                            Text = "went"
                        },
                        new
                        {
                            Id = 18,
                            AnswerText = "go",
                            IsCorrect = false,
                            OrderNumber = 2,
                            QuestionId = 5,
                            Text = "go"
                        },
                        new
                        {
                            Id = 19,
                            AnswerText = "goes",
                            IsCorrect = false,
                            OrderNumber = 3,
                            QuestionId = 5,
                            Text = "goes"
                        },
                        new
                        {
                            Id = 20,
                            AnswerText = "going",
                            IsCorrect = false,
                            OrderNumber = 4,
                            QuestionId = 5,
                            Text = "going"
                        },
                        new
                        {
                            Id = 21,
                            AnswerText = "v = s/t",
                            IsCorrect = true,
                            OrderNumber = 1,
                            QuestionId = 6,
                            Text = "v = s/t"
                        },
                        new
                        {
                            Id = 22,
                            AnswerText = "v = s*t",
                            IsCorrect = false,
                            OrderNumber = 2,
                            QuestionId = 6,
                            Text = "v = s*t"
                        },
                        new
                        {
                            Id = 23,
                            AnswerText = "v = t/s",
                            IsCorrect = false,
                            OrderNumber = 3,
                            QuestionId = 6,
                            Text = "v = t/s"
                        },
                        new
                        {
                            Id = 24,
                            AnswerText = "v = s + t",
                            IsCorrect = false,
                            OrderNumber = 4,
                            QuestionId = 6,
                            Text = "v = s + t"
                        });
                });

            modelBuilder.Entity("MyAspNetCoreApp.Models.Exam", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.Property<int>("Duration")
                        .HasColumnType("int");

                    b.Property<bool>("IsActive")
                        .HasColumnType("bit");

                    b.Property<string>("Title")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<int>("TotalQuestions")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.ToTable("Exams");

                    b.HasData(
                        new
                        {
                            Id = 1,
                            CreatedDate = new DateTime(2024, 6, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            Description = "Bài kiểm tra kiến thức toán học cơ bản",
                            Duration = 90,
                            IsActive = true,
                            Title = "Đề thi Toán học",
                            TotalQuestions = 20
                        },
                        new
                        {
                            Id = 2,
                            CreatedDate = new DateTime(2024, 6, 2, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            Description = "Bài kiểm tra năng lực tiếng Anh",
                            Duration = 60,
                            IsActive = true,
                            Title = "Đề thi Tiếng Anh",
                            TotalQuestions = 30
                        },
                        new
                        {
                            Id = 3,
                            CreatedDate = new DateTime(2024, 6, 3, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            Description = "Bài kiểm tra kiến thức vật lý",
                            Duration = 90,
                            IsActive = true,
                            Title = "Đề thi Vật lý",
                            TotalQuestions = 25
                        });
                });

            modelBuilder.Entity("MyAspNetCoreApp.Models.ExamResult", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<int>("CorrectAnswers")
                        .HasColumnType("int");

                    b.Property<int>("ExamId")
                        .HasColumnType("int");

                    b.Property<int?>("SchoolId")
                        .HasColumnType("int");

                    b.Property<int>("Score")
                        .HasColumnType("int");

                    b.Property<DateTime>("TakenDate")
                        .HasColumnType("datetime2");

                    b.Property<TimeSpan>("TimeTaken")
                        .HasColumnType("time");

                    b.Property<int>("TotalQuestions")
                        .HasColumnType("int");

                    b.Property<string>("UserId")
                        .IsRequired()
                        .HasColumnType("nvarchar(450)");

                    b.Property<int?>("UserId1")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("ExamId");

                    b.HasIndex("SchoolId");

                    b.HasIndex("UserId");

                    b.HasIndex("UserId1");

                    b.ToTable("ExamResults");
                });

            modelBuilder.Entity("MyAspNetCoreApp.Models.Product", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.Property<string>("ImageUrl")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<bool>("IsFeatured")
                        .HasColumnType("bit");

                    b.Property<bool>("IsNew")
                        .HasColumnType("bit");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<decimal>("Price")
                        .HasColumnType("decimal(18,2)");

                    b.HasKey("Id");

                    b.ToTable("Products");

                    b.HasData(
                        new
                        {
                            Id = 1,
                            CreatedDate = new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            Description = "Trường đại học công nghệ hàng đầu với nhiều ngành học hot",
                            ImageUrl = "/image/HUTECH2.jpg",
                            IsFeatured = true,
                            IsNew = false,
                            Name = "ĐẠI HỌC CÔNG NGHỆ TP.HCM",
                            Price = 25000000m
                        },
                        new
                        {
                            Id = 2,
                            CreatedDate = new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            Description = "Trường đại học chuyên về khoa học xã hội và nhân văn",
                            ImageUrl = "/image/nhanvan.png",
                            IsFeatured = true,
                            IsNew = true,
                            Name = "ĐẠI HỌC XÃ HỘI VÀ NHÂN VĂN",
                            Price = 20000000m
                        },
                        new
                        {
                            Id = 3,
                            CreatedDate = new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            Description = "Trường đại học kỹ thuật hàng đầu Việt Nam",
                            ImageUrl = "/image/bachkhoa.jpg",
                            IsFeatured = false,
                            IsNew = true,
                            Name = "ĐẠI HỌC BÁCH KHOA TP.HCM",
                            Price = 30000000m
                        });
                });

            modelBuilder.Entity("MyAspNetCoreApp.Models.Question", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<int>("ExamId")
                        .HasColumnType("int");

                    b.Property<int>("OrderNumber")
                        .HasColumnType("int");

                    b.Property<int>("Points")
                        .HasColumnType("int");

                    b.Property<string>("QuestionText")
                        .IsRequired()
                        .HasMaxLength(1000)
                        .HasColumnType("nvarchar(1000)");

                    b.Property<int>("Type")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("ExamId");

                    b.ToTable("Questions");

                    b.HasData(
                        new
                        {
                            Id = 1,
                            ExamId = 1,
                            OrderNumber = 1,
                            Points = 5,
                            QuestionText = "Tính đạo hàm của hàm số f(x) = x² + 3x - 2",
                            Type = 1
                        },
                        new
                        {
                            Id = 2,
                            ExamId = 1,
                            OrderNumber = 2,
                            Points = 3,
                            QuestionText = "Giải phương trình: 2x + 5 = 13",
                            Type = 1
                        },
                        new
                        {
                            Id = 3,
                            ExamId = 1,
                            OrderNumber = 3,
                            Points = 7,
                            QuestionText = "Tính tích phân ∫(2x + 1)dx từ 0 đến 2",
                            Type = 1
                        },
                        new
                        {
                            Id = 4,
                            ExamId = 2,
                            OrderNumber = 1,
                            Points = 2,
                            QuestionText = "Choose the correct form: 'I _____ to school every day.'",
                            Type = 1
                        },
                        new
                        {
                            Id = 5,
                            ExamId = 2,
                            OrderNumber = 2,
                            Points = 2,
                            QuestionText = "What is the past tense of 'go'?",
                            Type = 1
                        },
                        new
                        {
                            Id = 6,
                            ExamId = 3,
                            OrderNumber = 1,
                            Points = 4,
                            QuestionText = "Công thức tính vận tốc là gì?",
                            Type = 1
                        });
                });

            modelBuilder.Entity("MyAspNetCoreApp.Models.School", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("Address")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.Property<int>("EstablishedYear")
                        .HasColumnType("int");

                    b.Property<string>("ImageUrl")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("PhoneNumber")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)");

                    b.Property<decimal>("TuitionFee")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("Type")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("Website")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.HasKey("Id");

                    b.ToTable("Schools");
                });

            modelBuilder.Entity("MyAspNetCoreApp.Models.Survey", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.Property<bool>("IsActive")
                        .HasColumnType("bit");

                    b.Property<string>("Title")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<int>("Type")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.ToTable("Surveys");

                    b.HasData(
                        new
                        {
                            Id = 1,
                            CreatedDate = new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            Description = "Discover your personality type with the Myers-Briggs Type Indicator",
                            IsActive = true,
                            Title = "MBTI Personality Assessment",
                            Type = 1
                        },
                        new
                        {
                            Id = 2,
                            CreatedDate = new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            Description = "Find careers that match your interests and skills",
                            IsActive = true,
                            Title = "Career Interest Survey",
                            Type = 2
                        });
                });

            modelBuilder.Entity("MyAspNetCoreApp.Models.SurveyAnswer", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("AnswerText")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<int>("OrderNumber")
                        .HasColumnType("int");

                    b.Property<string>("PersonalityType")
                        .IsRequired()
                        .HasMaxLength(10)
                        .HasColumnType("nvarchar(10)");

                    b.Property<int>("Points")
                        .HasColumnType("int");

                    b.Property<int>("QuestionId")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("QuestionId");

                    b.ToTable("SurveyAnswers");

                    b.HasData(
                        new
                        {
                            Id = 1,
                            AnswerText = "Giao tiếp với nhiều người",
                            OrderNumber = 1,
                            PersonalityType = "E",
                            Points = 1,
                            QuestionId = 1
                        },
                        new
                        {
                            Id = 2,
                            AnswerText = "Chỉ với một vài người thân thiết",
                            OrderNumber = 2,
                            PersonalityType = "I",
                            Points = 1,
                            QuestionId = 1
                        },
                        new
                        {
                            Id = 3,
                            AnswerText = "Tập trung vào thực tế",
                            OrderNumber = 1,
                            PersonalityType = "S",
                            Points = 1,
                            QuestionId = 2
                        },
                        new
                        {
                            Id = 4,
                            AnswerText = "Tập trung vào khả năng và ý tưởng",
                            OrderNumber = 2,
                            PersonalityType = "N",
                            Points = 1,
                            QuestionId = 2
                        },
                        new
                        {
                            Id = 5,
                            AnswerText = "Dựa vào logic",
                            OrderNumber = 1,
                            PersonalityType = "T",
                            Points = 1,
                            QuestionId = 3
                        },
                        new
                        {
                            Id = 6,
                            AnswerText = "Dựa vào cảm xúc",
                            OrderNumber = 2,
                            PersonalityType = "F",
                            Points = 1,
                            QuestionId = 3
                        },
                        new
                        {
                            Id = 7,
                            AnswerText = "Có kế hoạch rõ ràng",
                            OrderNumber = 1,
                            PersonalityType = "J",
                            Points = 1,
                            QuestionId = 4
                        },
                        new
                        {
                            Id = 8,
                            AnswerText = "Linh hoạt thay đổi",
                            OrderNumber = 2,
                            PersonalityType = "P",
                            Points = 1,
                            QuestionId = 4
                        },
                        new
                        {
                            Id = 9,
                            AnswerText = "Nói chuyện với nhiều người",
                            OrderNumber = 1,
                            PersonalityType = "E",
                            Points = 1,
                            QuestionId = 5
                        },
                        new
                        {
                            Id = 10,
                            AnswerText = "Tìm góc yên tĩnh để thư giãn",
                            OrderNumber = 2,
                            PersonalityType = "I",
                            Points = 1,
                            QuestionId = 5
                        });
                });

            modelBuilder.Entity("MyAspNetCoreApp.Models.SurveyQuestion", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("Category")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<int>("OrderNumber")
                        .HasColumnType("int");

                    b.Property<string>("QuestionText")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.Property<int>("SurveyId")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("SurveyId");

                    b.ToTable("SurveyQuestions");

                    b.HasData(
                        new
                        {
                            Id = 1,
                            Category = "E/I",
                            OrderNumber = 1,
                            QuestionText = "Bạn thích giao tiếp với nhiều người hay chỉ với một vài người thân thiết?",
                            SurveyId = 1
                        },
                        new
                        {
                            Id = 2,
                            Category = "S/N",
                            OrderNumber = 2,
                            QuestionText = "Bạn thích tập trung vào thực tế hay vào khả năng và ý tưởng?",
                            SurveyId = 1
                        },
                        new
                        {
                            Id = 3,
                            Category = "T/F",
                            OrderNumber = 3,
                            QuestionText = "Khi đưa ra quyết định, bạn dựa vào logic hay cảm xúc?",
                            SurveyId = 1
                        },
                        new
                        {
                            Id = 4,
                            Category = "J/P",
                            OrderNumber = 4,
                            QuestionText = "Bạn thích có kế hoạch rõ ràng hay linh hoạt thay đổi?",
                            SurveyId = 1
                        },
                        new
                        {
                            Id = 5,
                            Category = "E/I",
                            OrderNumber = 5,
                            QuestionText = "Trong một buổi tiệc, bạn thường:",
                            SurveyId = 1
                        });
                });

            modelBuilder.Entity("MyAspNetCoreApp.Models.SurveyResult", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<DateTime>("CompletedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasMaxLength(1000)
                        .HasColumnType("nvarchar(1000)");

                    b.Property<string>("Result")
                        .IsRequired()
                        .HasMaxLength(10)
                        .HasColumnType("nvarchar(10)");

                    b.Property<int>("SurveyId")
                        .HasColumnType("int");

                    b.Property<int>("TotalScore")
                        .HasColumnType("int");

                    b.Property<string>("UserId")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.HasIndex("SurveyId");

                    b.ToTable("SurveyResults");
                });

            modelBuilder.Entity("MyAspNetCoreApp.Models.User", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("Email")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("FullName")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<bool>("IsActive")
                        .HasColumnType("bit");

                    b.Property<string>("Password")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.HasKey("Id");

                    b.ToTable("AppUsers");
                });

            modelBuilder.Entity("MyAspNetCoreApp.Models.UserAnswer", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<int?>("AnswerId")
                        .HasColumnType("int");

                    b.Property<string>("AnswerText")
                        .HasMaxLength(2000)
                        .HasColumnType("nvarchar(2000)");

                    b.Property<DateTime>("AnsweredAt")
                        .HasColumnType("datetime2");

                    b.Property<bool>("IsCorrect")
                        .HasColumnType("bit");

                    b.Property<int>("QuestionId")
                        .HasColumnType("int");

                    b.Property<string>("UserId")
                        .IsRequired()
                        .HasColumnType("nvarchar(450)");

                    b.Property<int?>("UserId1")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("AnswerId");

                    b.HasIndex("QuestionId");

                    b.HasIndex("UserId");

                    b.HasIndex("UserId1");

                    b.ToTable("UserAnswers");
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityRoleClaim<string>", b =>
                {
                    b.HasOne("Microsoft.AspNetCore.Identity.IdentityRole", null)
                        .WithMany()
                        .HasForeignKey("RoleId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserClaim<string>", b =>
                {
                    b.HasOne("Microsoft.AspNetCore.Identity.IdentityUser", null)
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserLogin<string>", b =>
                {
                    b.HasOne("Microsoft.AspNetCore.Identity.IdentityUser", null)
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserRole<string>", b =>
                {
                    b.HasOne("Microsoft.AspNetCore.Identity.IdentityRole", null)
                        .WithMany()
                        .HasForeignKey("RoleId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Microsoft.AspNetCore.Identity.IdentityUser", null)
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserToken<string>", b =>
                {
                    b.HasOne("Microsoft.AspNetCore.Identity.IdentityUser", null)
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("MyAspNetCoreApp.Models.Answer", b =>
                {
                    b.HasOne("MyAspNetCoreApp.Models.Question", "Question")
                        .WithMany("Answers")
                        .HasForeignKey("QuestionId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Question");
                });

            modelBuilder.Entity("MyAspNetCoreApp.Models.ExamResult", b =>
                {
                    b.HasOne("MyAspNetCoreApp.Models.Exam", "Exam")
                        .WithMany("ExamResults")
                        .HasForeignKey("ExamId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("MyAspNetCoreApp.Models.School", "School")
                        .WithMany("ExamResults")
                        .HasForeignKey("SchoolId");

                    b.HasOne("Microsoft.AspNetCore.Identity.IdentityUser", null)
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("MyAspNetCoreApp.Models.User", null)
                        .WithMany("ExamResults")
                        .HasForeignKey("UserId1");

                    b.Navigation("Exam");

                    b.Navigation("School");
                });

            modelBuilder.Entity("MyAspNetCoreApp.Models.Question", b =>
                {
                    b.HasOne("MyAspNetCoreApp.Models.Exam", "Exam")
                        .WithMany("Questions")
                        .HasForeignKey("ExamId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Exam");
                });

            modelBuilder.Entity("MyAspNetCoreApp.Models.SurveyAnswer", b =>
                {
                    b.HasOne("MyAspNetCoreApp.Models.SurveyQuestion", "Question")
                        .WithMany("Answers")
                        .HasForeignKey("QuestionId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Question");
                });

            modelBuilder.Entity("MyAspNetCoreApp.Models.SurveyQuestion", b =>
                {
                    b.HasOne("MyAspNetCoreApp.Models.Survey", "Survey")
                        .WithMany("Questions")
                        .HasForeignKey("SurveyId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Survey");
                });

            modelBuilder.Entity("MyAspNetCoreApp.Models.SurveyResult", b =>
                {
                    b.HasOne("MyAspNetCoreApp.Models.Survey", "Survey")
                        .WithMany("Results")
                        .HasForeignKey("SurveyId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Survey");
                });

            modelBuilder.Entity("MyAspNetCoreApp.Models.UserAnswer", b =>
                {
                    b.HasOne("MyAspNetCoreApp.Models.Answer", "Answer")
                        .WithMany("UserAnswers")
                        .HasForeignKey("AnswerId")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.HasOne("MyAspNetCoreApp.Models.Question", "Question")
                        .WithMany("UserAnswers")
                        .HasForeignKey("QuestionId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Microsoft.AspNetCore.Identity.IdentityUser", null)
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("MyAspNetCoreApp.Models.User", null)
                        .WithMany("UserAnswers")
                        .HasForeignKey("UserId1");

                    b.Navigation("Answer");

                    b.Navigation("Question");
                });

            modelBuilder.Entity("MyAspNetCoreApp.Models.Answer", b =>
                {
                    b.Navigation("UserAnswers");
                });

            modelBuilder.Entity("MyAspNetCoreApp.Models.Exam", b =>
                {
                    b.Navigation("ExamResults");

                    b.Navigation("Questions");
                });

            modelBuilder.Entity("MyAspNetCoreApp.Models.Question", b =>
                {
                    b.Navigation("Answers");

                    b.Navigation("UserAnswers");
                });

            modelBuilder.Entity("MyAspNetCoreApp.Models.School", b =>
                {
                    b.Navigation("ExamResults");
                });

            modelBuilder.Entity("MyAspNetCoreApp.Models.Survey", b =>
                {
                    b.Navigation("Questions");

                    b.Navigation("Results");
                });

            modelBuilder.Entity("MyAspNetCoreApp.Models.SurveyQuestion", b =>
                {
                    b.Navigation("Answers");
                });

            modelBuilder.Entity("MyAspNetCoreApp.Models.User", b =>
                {
                    b.Navigation("ExamResults");

                    b.Navigation("UserAnswers");
                });
#pragma warning restore 612, 618
        }
    }
}
